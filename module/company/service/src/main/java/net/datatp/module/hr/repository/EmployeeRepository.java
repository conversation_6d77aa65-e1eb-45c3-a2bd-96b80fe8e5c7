package net.datatp.module.hr.repository;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.repository.DataTPRepository;
import net.datatp.module.hr.entity.Employee;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface EmployeeRepository extends DataTPRepository<Employee, Serializable> {
  @Query("SELECT e FROM Employee e JOIN Account aa ON aa.id = e.accountId WHERE e.companyId = :companyId " +
    "AND lower(aa.loginId) = lower(:loginId) AND e.storageState = 'ACTIVE'")
  Employee getByLoginId(@Param("companyId") Long companyId, @Param("loginId") String loginId);

  @Query("SELECT e FROM Employee e WHERE e.companyId = :companyId AND e.accountId = :accountId AND e.storageState = 'ACTIVE'")
  Employee getByAccountId(@Param("companyId") Long companyId, @Param("accountId") Long accountId);

  @Query("SELECT e from Employee e, HRDepartmentMembership r WHERE e.id = r.employeeId AND r.departmentId = :departmentId")
  List<Employee> findByDepartmentId(@Param("departmentId") Long departmentId);

  @Query("SELECT e FROM Employee e WHERE e.id IN :ids")
  List<Employee> findByIds(@Param("ids") List<Long> ids);

  @Query("SELECT e FROM Employee e WHERE e.companyId = :companyId")
  List<Employee> findByCompanyId(@Param("companyId") Long companyId);

  @Modifying
  @Query("update Employee e SET e.storageState = :state WHERE e.id IN :ids")
  int updateStorageState(@Param("ids") Collection<Long> ids, @Param("state") StorageState state);

  @Query("SELECT e FROM Employee e WHERE e.companyId = :companyId AND e.bfsoneCode = :code AND e.storageState = 'ACTIVE'")
  Employee getByBFSOneCode(@Param("companyId") Long companyId, @Param("code") String code);

  @Query("SELECT e FROM Employee e WHERE e.companyId = :companyId AND e.employeeCode = :employeeCode AND e.storageState = 'ACTIVE'")
  Employee getByEmployeeCode(@Param("companyId") Long companyId, @Param("employeeCode") String employeeCode);
  
  @Query("SELECT e FROM Employee e WHERE lower(e.bfsoneCode) = lower(:code) AND e.storageState = 'ACTIVE'")
  List<Employee> findByBFSOneCode(@Param("code") String code);

  @Query("SELECT e FROM Employee e WHERE e.companyId = :companyId AND lower(e.bfsoneUsername) = lower(:username) AND e.storageState = 'ACTIVE'")
  Employee getByBFSOneUsername(@Param("companyId") Long companyId, @Param("username") String username);

  @Query("SELECT e FROM Employee e WHERE lower(e.bfsoneUsername) = lower(:username) AND e.storageState = 'ACTIVE'")
  List<Employee> findByBFSOneUsername(@Param("username") String username);

}