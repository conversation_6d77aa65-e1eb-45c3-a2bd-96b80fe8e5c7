package net.datatp.module.hr.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.account.entity.UserProfile;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Entity
@Table(name = Employee.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = Employee.TABLE_NAME + "_bfsone_code",
      columnNames = {"storage_state", "company_id", "bfsone_code"}
    ),
    @UniqueConstraint(
      name = Employee.TABLE_NAME + "_bfsone_user_name",
      columnNames = {"storage_state", "company_id", "bfsone_username"}
    ),
    @UniqueConstraint(
      name = Employee.TABLE_NAME + "_employee_code_company",
      columnNames = {"employee_code", "company_id"}
    ),
    @UniqueConstraint(
      name = Employee.TABLE_NAME + "_account_company",
      columnNames = {"account_id", "company_id"}
    ),
  },
  indexes = {
    @Index(
      name = Employee.TABLE_NAME + "_bfsone_code_idx",
      columnList = "bfsone_code"
    ),
    @Index(
      name = Employee.TABLE_NAME + "_bfsone_username_idx",
      columnList = "bfsone_username"
    ),
  }
)
@Getter
@Setter
@NoArgsConstructor
public class Employee extends CompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "company_hr_employee";

  @NotNull
  @Column(name = "account_id")
  private Long accountId;

  @Column(name = "bfsone_username")
  private String bfsoneUsername;

  @Column(name = "bfsone_code")
  private String bfsoneCode;

  @Column(name = "employee_card_id")
  private String employeeCardId;

  //Ex: LE VAN THAO - HPTHAOLV
  private String label;

  @Column(name = "employee_tax_code")
  private String employeeTaxCode;

  @Column(name = "employee_code")
  private String employeeCode;

  @Column(name = "company_branch")
  private String companyBranch;

  @Column(name = "manager_employee_id")
  private Long managerEmployeeId;

  @Column(name = "manager_employee_label")
  private String managerEmployeeLabel;

  private int priority = 5;

  private String description;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "start_date")
  private Date startDate;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "end_date")
  private Date endDate;

  @Column(name = "variants")
  private String variants;

  public Employee(Account account) {
    this.accountId = account.getId();
    this.label = account.getFullName();
  }

  public Employee(UserProfile profile) {
    this.label = profile.getFullName();
  }

  public Employee withLabel(String label) {
    this.label = label;
    return this;
  }

  public Employee withEmployeeTaxCode(String taxCode) {
    this.employeeTaxCode = taxCode;
    return this;
  }

  public Employee withPriority(int priority) {
    this.priority = priority;
    return this;
  }

  public Employee withDescription(String description) {
    this.description = description;
    return this;
  }

  public Employee withStartDate(Date startDate) {
    this.startDate = startDate;
    return this;
  }

  public Employee withEndDate(Date endDate) {
    this.endDate = endDate;
    return this;
  }

  public String identify() { return bfsoneUsername; }

  public Employee withBFSOneUsername(String username) {
    this.bfsoneUsername = username;
    return this;
  }

  public Employee withBFSOneCode(String code) {
    this.bfsoneCode = code;
    return this;
  }

  public void setVariants(String text) {
    if(text == null) {
      this.variants = null;
    } else {
      text = text.toLowerCase();
      Set<String> variantSet = StringUtil.toStringHashSet(text);
      this.variants = StringUtil.join(variantSet);
    }
  }

  public void setVariants(Set<String> set) {
    if(set == null) {
      this.variants = null;
    } else {
      this.variants = StringUtil.join(set);
    }
  }

  public Set<String> variantSet() {
    if(variants == null) return new HashSet<>();
    return StringUtil.toStringHashSet(variants);
  }
}