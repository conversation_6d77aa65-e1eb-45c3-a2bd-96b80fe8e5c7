package net.datatp.module.hr;

import groovy.lang.Binding;
import jakarta.annotation.PostConstruct;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.NewAccountModel;
import net.datatp.module.account.entity.Account;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.CompanyAclModel;
import net.datatp.module.core.security.entity.AccessType;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryManager.QueryContext;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.hr.entity.HRDepartment;
import net.datatp.module.hr.entity.HRDepartmentMembership;
import net.datatp.module.hr.plugin.EmployeeServicePlugin;
import net.datatp.module.hr.plugin.HRServicePlugin;
import net.datatp.module.hr.repository.EmployeeRepository;
import net.datatp.module.hr.repository.HRDepartmentMembershipRepository;
import net.datatp.module.hr.repository.HRDepartmentRepository;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Component;

@Component
public class EmployeeLogic extends DAOService {
  private String QUERY_SCRIPT_DIR;

  @Autowired
  @Getter
  private EmployeeRepository employeeRepo;

  @Autowired
  private HRDepartmentMembershipRepository deptEmplRelRepo;

  @Autowired
  private HRDepartmentRepository departmentRepo;

  @Autowired
  @Getter
  private EmployeeReadLogic employeeReadLogic;

  @Autowired
  @Getter
  private AccountLogic accountLogic;

  @Autowired
  private DepartmentLogic departmentLogic;

  @PostConstruct
  public void onInit() {
    if ("test".equals(env)) return;
    QUERY_SCRIPT_DIR = appEnv.addonPath("core", "groovy/company/");
  }

  @Autowired(required = false)
  private final List<HRServicePlugin> plugins = new ArrayList<>();

  @Autowired(required = false)
  private final List<EmployeeServicePlugin> employeePlugins = new ArrayList<>();

  public Employee createEmployee(ClientInfo clientInfo, Company company, Account account, Employee employee) {
    employee.setAccountId(account.getId());
    for (EmployeeServicePlugin employeePlugin : employeePlugins) {
      employeePlugin.onPreSave(clientInfo, company, employee, true);
    }
    Employee savedEmployee = employeeRepo.save(clientInfo, company, employee);
    for (EmployeeServicePlugin employeePlugin : employeePlugins) {
      employeePlugin.onPostSave(clientInfo, company, savedEmployee, true);
    }
    return savedEmployee;
  }


  public Employee newEmployee(ClientInfo clientInfo, Company company, EmployeeCreationModel model) {
    try {
      Account account;
      if (Objects.isNull(model.getAccountId())) {
        account = model.toAccount();
      } else {
        account = accountLogic.getAccountById(clientInfo, model.getAccountId());
      }
      NewAccountModel newAccountModel = new NewAccountModel(account);

      if (account.isNew()) {
        accountLogic.createNewAccount(clientInfo, newAccountModel);
        account = accountLogic.getEditable(clientInfo, account.getLoginId());
      }

      Employee exitstEmployee = employeeReadLogic.getByAccount(clientInfo, company, account.getId());
      if (Objects.nonNull(exitstEmployee)) return exitstEmployee;

      Employee employee = new Employee(account);
      employee.withBFSOneCode(model.getBfsoneCode());
      employee.withBFSOneUsername(model.getBfsoneUsername());
      employee.withPriority(model.getPriority());
      Employee savedEmployee = employeeRepo.save(clientInfo, company, employee);

      HRDepartment department = departmentLogic.getHRDepartment(clientInfo, company, model.getDepartmentId());
      HRDepartmentMembership relation = new HRDepartmentMembership(department, savedEmployee);
      deptEmplRelRepo.save(clientInfo, company, relation);

      return savedEmployee;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  public Employee createEmployee(ClientInfo clientInfo, Company company, NewEmployeeModel model) {
    Account account = model.getAccount();
    if (account.isNew()) {
      accountLogic.createNewAccount(clientInfo, model);
      account = accountLogic.getEditable(clientInfo, account.getLoginId());
    }

    Employee exitstEmployee = employeeReadLogic.getByAccount(clientInfo, company, account.getId());
    if (Objects.nonNull(exitstEmployee)) {
      return exitstEmployee;
    }

    Employee employee = new Employee(account);
    for (EmployeeServicePlugin employeePlugin : employeePlugins) {
      employeePlugin.onPreSave(clientInfo, company, employee, true);
    }
    Employee savedEmployee = employeeRepo.save(clientInfo, company, employee);

    if (Objects.nonNull(model.getDepartmentIds())) {
      for (Long departmentId : model.getDepartmentIds()) {
        HRDepartment department = departmentRepo.getById(departmentId);
        HRDepartmentMembership relation = new HRDepartmentMembership(department, savedEmployee);
        deptEmplRelRepo.save(clientInfo, company, relation);
      }
    }
    for (EmployeeServicePlugin employeePlugin : employeePlugins) {
      employeePlugin.onPostSave(clientInfo, company, savedEmployee, true);
    }
    return savedEmployee;
  }

  public Employee saveEmployee(ClientInfo clientInfo, Company company, Employee employee) {
    return employeeRepo.save(clientInfo, company, employee);
  }

  public Employee getEmployeeByBFSOneCode(ClientInfo clientInfo, Company company, String code) {
    return employeeReadLogic.getEmployeeByBFSOneCode(clientInfo, company, code);
  }

  //TODO: Not possible as one employee per code. Should use get
  public List<Employee> findEmployeeByBFSOneCode(ClientInfo clientInfo, String code) {
    return employeeReadLogic.findEmployeeByBFSOneCode(clientInfo, code);
  }

  public Employee getByAccount(ClientInfo client, Company company, Long accountId) {
    return employeeReadLogic.getByAccount(client, company, accountId);
  }

  public Employee getEmployeeByBFSOneUsername(ClientInfo clientInfo, Company company, String username) {
    return employeeReadLogic.getEmployeeByBFSOneUsername(clientInfo, company, username);
  }

  public List<Employee> findEmployeeByBFSOneUsername(ClientInfo clientInfo, String username) {
    return employeeReadLogic.findEmployeeByBFSOneUsername(clientInfo, username);
  }

  public Employee getEmployee(ClientInfo clientInfo, Company company, Long id) {
    return employeeReadLogic.getEmployee(clientInfo, company, id);
  }

  public Employee getEmployee(ClientInfo clientInfo, Company company, String loginId) {
    return employeeReadLogic.getEmployee(clientInfo, company, loginId);
  }

  public List<Employee> findEmployees(Long departmentId) {
    return employeeReadLogic.findEmployees(departmentId);
  }

  public List<Employee> findEmployees(ClientInfo client, Company company) {
    return employeeReadLogic.findEmployees(client, company);
  }

  public List<SqlMapRecord> searchEmployees(ClientInfo client, Company company, SqlQueryParams params) {
    return employeeReadLogic.searchEmployees(client, company, params);
  }

  public List<SqlMapRecord> findEmployeeIdsInDepartment(ClientInfo client, Company company, SqlQueryParams params) {
    return employeeReadLogic.findEmployeeIdsInDepartment(client, company, params);
  }

  public List<Long> findEmployeesByManagerId(ClientInfo client, Company company, Long accessEmployeeId) {
    QueryContext queryContext = sqlQueryManager.create(QUERY_SCRIPT_DIR, "FindEmployeeByDeptQuery.groovy");
    MapSqlParameterSource params = new MapSqlParameterSource();
    params.addValue("manager_employee_id", accessEmployeeId);
    params.addValue("company_id", company.getId());
    final SqlSelectView view = queryContext.createSqlSelectView(new Binding(), params);
    final List<SqlMapRecord> records = view.getSqlMapRecords();
    List<Long> result = new ArrayList<>();
    for (SqlMapRecord record : records) {
      Long employeeId = record.getLong("participant_employee_id", null);
      if (employeeId == null) continue;
      result.add(employeeId);
    }
    return result;
  }

  //Need to review this function
  public boolean changeStorageState(ClientInfo client, Company company, ChangeStorageStateRequest req) {
    List<Employee> employees = employeeRepo.findByIds(req.getEntityIds());
    for (Employee employee : employees) {
      changeStorageState(client, company, employee, req.getNewStorageState());
    }
    return true;
  }

  //Need to review this function
  public boolean changeStorageState(ClientInfo client, Company company, Employee employee, StorageState state) {
    plugins.forEach(plugin -> {
      plugin.onPreStateChange(client, company, employee, state);
    });
    employeeRepo.updateStorageState(Arrays.asList(employee.getId()), state);

    plugins.forEach(plugin -> {
      plugin.onPostStateChange(client, company, employee, state);
    });
    return true;
  }

  public List<CompanyAclModel> findEmployeeCompanyAcls(ClientInfo client, AccessType accessType, String loginId) {
    return employeeReadLogic.findEmployeeCompanyAcls(client, accessType, loginId);
  }

  public HRDepartmentMembership createEmployeeDepartmentRelation(ClientInfo client, Company company, Employee empl, HRDepartment dept) {
    return deptEmplRelRepo.save(new HRDepartmentMembership(dept, empl));
  }

  public Employee getCompanyEmployee(ClientInfo client, Company company) {
    return employeeReadLogic.getCompanyEmployee(client, company);
  }

  public int deleteEmployees(ClientInfo client, Company company, List<Long> targetIds) {
    List<Employee> employeeList = employeeRepo.findByIds(targetIds);
    for (Employee employee : employeeList) {
      deptEmplRelRepo.findByEmployeeId(employee.getId());
      employeeRepo.delete(employee);
    }
    return 0;
  }

}