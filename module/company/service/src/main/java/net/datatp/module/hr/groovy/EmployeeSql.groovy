package net.datatp.module.hr.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

class EmployeeSql extends Executor {

    public class SearchEmployee extends ExecutableSqlBuilder {
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String filterClause =  "(emp.bfsone_username) LIKE '%' || LOWER(:searchPattern) OR (emp.variants) LIKE '%' || LOWER(:searchPattern)";

            String query = """
                WITH department AS (
                  SELECT 
                    dept.id               AS dept_id, 
                    dept."name"           AS name, 
                    dept.type             AS type, 
                    dept."label"          AS label, 
                    dept_ref."role"       AS role,     
                    dept_ref.employee_id  AS employee_id  
                  FROM company_hr_department dept 
                    JOIN company_hr_department_employee_rel dept_ref 
                      ON dept_ref.department_id = dept.id 
                  WHERE 
                    ${FILTER_BY_STORAGE_STATE('dept', sqlParams)}
                    ${AND_FILTER_BY_PARAM(" dept.company_id", "companyId", sqlParams)}
                )
                SELECT 
                  DISTINCT ON (emp.id)
                  emp.*,
                  aa.login_id                     AS login_id,
                  dept.dept_id                    AS department_id,
                  dept.name                       AS department_name,
                  dept.type                       AS department_type,
                  dept.label                      AS department_label,
                  dept.role                       AS employee_role
                FROM company_hr_employee emp
                  JOIN account_account aa
                    ON aa.id = emp.account_id
                  LEFT JOIN department dept 
                    ON dept.employee_id = emp.id
                WHERE 
                  ${FILTER_BY_STORAGE_STATE('emp', sqlParams)}
                  ${AND_FILTER_BY_PARAM("emp.company_id", "companyId", sqlParams)}
                  ${AND_FILTER_BY_PARAM("dept.dept_id", "departmentId", sqlParams)}
                  ${AND_FILTER_BY_PARAM("dept.name", "departmentName", sqlParams)}
                  ${addAndClause(sqlParams, "searchPattern", filterClause)}
                ORDER BY emp.id, dept.name, dept.role, emp.label ASC, emp.modified_time DESC
                ${MAX_RETURN(sqlParams)}
            """;
            return query;
        }
    }

    public class FindEmployeeIdsByManagerId extends ExecutableSqlBuilder {
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            String query = """
                SELECT DISTINCT e.account_id
                FROM company_hr_employee e
                JOIN company_hr_department_employee_rel der ON e.id = der.employee_id
                JOIN company_hr_department d ON der.department_id = d.id 
                WHERE e.company_id = :companyId 
                    AND e.storage_state = 'ACTIVE'
                    AND EXISTS (
                        SELECT 1
                        FROM company_hr_department_employee_rel der2
                        JOIN company_hr_employee e2 
                            ON der2.employee_id = e2.id
                        WHERE der2.department_id = d.id 
                            AND e2.company_id = :companyId 
                            AND e2.account_id = :accessAccountId
                    )
            """;
            return query;
        }
    }

    public class findEmployeeIdsInDepartment extends ExecutableSqlBuilder {
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            String query = """
                  SELECT DISTINCT ON (e.id) 
                        e.id            AS employee_id,
                        e.account_id    AS employee_account_id,
                        e."label"       AS employee_label,
                        aa.email        AS employee_email,
                        der."role"      AS employee_role,
                        d."name"        AS department_name,
                        d."label"       AS department_label
                  FROM company_hr_employee e
                    JOIN account_account aa ON aa.id = e.account_id
                    JOIN company_hr_department_employee_rel der ON e.id = der.employee_id
                    JOIN company_hr_department d ON der.department_id = d.id 
                  WHERE e.company_id = :companyId 
                    AND e.storage_state = 'ACTIVE'
                    AND EXISTS (
                        SELECT 1
                        FROM company_hr_department_employee_rel der2
                          JOIN company_hr_employee e2 ON der2.employee_id = e2.id
                        WHERE der2.department_id = d.id 
                          AND e2.company_id = :companyId 
                          ${AND_FILTER_BY_PARAM("e2.account_id", "accessAccountId", ctx.getParam("sqlParams"))}
                    )
                  ORDER BY e.id, d."name", der."role";
            """;
            return query;
        }
    }

    public EmployeeSql() {
        register(new SearchEmployee())
        register(new findEmployeeIdsInDepartment())
        register(new FindEmployeeIdsByManagerId())
    }
}