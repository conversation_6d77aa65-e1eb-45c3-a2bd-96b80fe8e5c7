package net.datatp.module.hr;

import lombok.Getter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.CompanyAclModel;
import net.datatp.module.core.security.entity.AccessType;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.hr.entity.HRDepartment;
import net.datatp.module.hr.entity.HRDepartmentMembership;
import net.datatp.module.hr.http.Params;
import net.datatp.module.service.BaseComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Getter
@Service("HRService")
public class HRService extends BaseComponent {
  @Autowired
  private DepartmentLogic departmentLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  //HR Department
  @Transactional(readOnly = true)
  public HRDepartment getHRDepartmentById(ClientInfo clientInfo, Company company, Long id) {
    return departmentLogic.getHRDepartment(clientInfo, company, id);
  }

  @Transactional(readOnly = true)
  public HRDepartment getHRDepartmentByName(ClientInfo clientInfo, Company company, String name) {
    return departmentLogic.getHRDepartment(clientInfo, company, name);
  }

  @Transactional(readOnly = true)
  public List<HRDepartment> findHRDepartment(ClientInfo clientInfo, Company company, String name) {
    return departmentLogic.findDepartmentByName(clientInfo, company, name);
  }

  @Transactional(readOnly = true)
  public List<HRDepartment> findHRDepartmentByParentId(ClientInfo client, Company company, Long parentId) {
    return departmentLogic.findHRDepartmentByParentId(client, company, parentId);
  }

  @Transactional
  public HRDepartment createHRDepartment(ClientInfo clientInfo, Company company, HRDepartment parentDept, HRDepartment dept) {
    return departmentLogic.createHRDepartment(clientInfo, company, parentDept, dept);
  }


  @Transactional
  public HRDepartment saveHRDepartment(ClientInfo clientInfo, Company company, HRDepartment hRDepartment) {
    return departmentLogic.saveHRDepartment(clientInfo, company, hRDepartment);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchHRDepartment(ClientInfo client, Company company, SqlQueryParams params) {
    return departmentLogic.searchHRDepartment(client, company, params);
  }

  @Transactional
  public Boolean deleteHRDepartment(ClientInfo clientInfo, Company company, Long id) {
    return departmentLogic.delete(clientInfo, company, id);
  }

  @Transactional
  public Boolean deleteAllHRDepartment(ClientInfo clientInfo, Company company) {
    return departmentLogic.deleteAllHrDepartment(clientInfo, company);
  }

  @Transactional
  public HRDepartmentMembership getRelationByDepartmentIdAndEmployeeId(ClientInfo clientInfo, Company company, Long departmentId, Long employeeId) {
    return departmentLogic.getRelationByDepartmentIdAndEmployeeId(clientInfo, company, departmentId, employeeId);
  }

  @Transactional
  public boolean createHRDepartmentRelations(ClientInfo clientInfo, Company company, Params.HrMembershipRequest request) {
    return departmentLogic.createHRDepartmentRelations(clientInfo, company, request);
  }

  @Transactional
  public boolean deleteHRDepartmentRelations(ClientInfo clientInfo, Company company, Long departmentId, List<Long> employeeIds) {
    return departmentLogic.deleteHRDepartmentRelations(clientInfo, company, departmentId, employeeIds);
  }

  //Employee
  @Transactional
  public Employee createEmployee(ClientInfo clientInfo, Company company, Account account, Employee employee) {
    return employeeLogic.createEmployee(clientInfo, company, account, employee);
  }

  @Transactional
  public Employee newEmployee(ClientInfo client, Company company, EmployeeCreationModel model) {
    return employeeLogic.newEmployee(client, company, model);
  }

  @Transactional
  public Employee createEmployee(ClientInfo client, Company company, NewEmployeeModel model) {
    return employeeLogic.createEmployee(client, company, model);
  }

  @Transactional
  public Employee saveEmployee(ClientInfo client, Company company, Employee employee) {
    return employeeLogic.saveEmployee(client, company, employee);
  }

  @Transactional(readOnly = true)
  public Employee getEmployeeByLoginId(ClientInfo clientInfo, Company company, String loginId) {
    return employeeLogic.getEmployee(clientInfo, company, loginId);
  }

  @Transactional(readOnly = true)
  public Employee getEmployee(ClientInfo clientInfo, Company company, Long id) {
    return employeeLogic.getEmployee(clientInfo, company, id);
  }

  @Transactional(readOnly = true)
  public Employee getEmployeeByAccount(ClientInfo clientInfo, Company company, Long accountId) {
    return employeeLogic.getByAccount(clientInfo, company, accountId);
  }

  @Transactional(readOnly = true)
  public Employee getEmployeeByBFSOneUsername(ClientInfo clientInfo, Company company, String code) {
    return employeeLogic.getEmployeeByBFSOneUsername(clientInfo, company, code);
  }

  @Transactional(readOnly = true)
  public List<Employee> findEmployeeByBFSOneUsername(ClientInfo clientInfo, String username) {
    return employeeLogic.findEmployeeByBFSOneUsername(clientInfo, username);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchEmployees(ClientInfo client, Company company, SqlQueryParams params) {
    return employeeLogic.searchEmployees(client, company, params);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> findEmployeeIdsInDepartment(ClientInfo client, Company company, SqlQueryParams params) {
    return employeeLogic.findEmployeeIdsInDepartment(client, company, params);
  }

  @Transactional(readOnly = true)
  public List<Employee> findEmployees(ClientInfo client, Company company) {
    return employeeLogic.findEmployees(client, company);
  }

  @Transactional
  public int deleteEmployees(ClientInfo client, Company company, List<Long> targetIds) {
    return employeeLogic.deleteEmployees(client, company, targetIds);
  }

  @Transactional(readOnly = true)
  public List<CompanyAclModel> findEmployeeCompanyAcls(ClientInfo client, AccessType accessType, String loginId) {
    return employeeLogic.findEmployeeCompanyAcls(client, accessType, loginId);
  }

  @Transactional
  public HRDepartmentMembership createEmployeeDepartmentRelation(ClientInfo client, Company company, Employee empl, HRDepartment dept) {
    return employeeLogic.createEmployeeDepartmentRelation(client, company, empl, dept);
  }

  @Transactional
  public boolean changeEmployeesStorageState(ClientInfo client, Company company, ChangeStorageStateRequest req) {
    return employeeLogic.changeStorageState(client, company, req);
  }

}