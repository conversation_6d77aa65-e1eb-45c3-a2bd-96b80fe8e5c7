package cloud.datatp.fforwarder.sales.common.entity;

import cloud.datatp.fforwarder.price.entity.AirTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaLclTransportCharge;
import cloud.datatp.fforwarder.price.entity.TransportFrequency;
import cloud.datatp.fforwarder.sales.common.quote.CustomerAirPriceGroup;
import cloud.datatp.fforwarder.sales.common.quote.CustomerSeaFCLPriceGroup;
import cloud.datatp.fforwarder.sales.common.quote.CustomerSeaLCLPriceGroup;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.sales.quotation.entity.QuotationAirTransportCharge;
import cloud.datatp.fforwarder.sales.quotation.entity.QuotationSeaTransportCharge;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.TransportationMode;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serial;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Entity
@Table(name = QuotationCharge.TABLE_NAME)
@JsonInclude(Include.NON_NULL)
@Setter @Getter @NoArgsConstructor
public class QuotationCharge extends CompanyEntity {

  @Serial
  private static final long serialVersionUID = 1L;
  public static final String TABLE_NAME = "lgc_sales_quotation_charge";

  public static Comparator<QuotationCharge> BY_CARRIER = Comparator.comparing(QuotationCharge::getCarrierLabel);

  @Column(name = "specific_quotation_id")
  private Long specificQuotationId;

  @Column(name = "reference_code")
  private String referenceCode; // rate ref

  @Column(name = "is_confirm")
  private Boolean isConfirm;

  @Enumerated(EnumType.STRING)
  @Column(name = "purpose")
  private Purpose purpose = Purpose.EXPORT;

  @Enumerated(EnumType.STRING)
  @Column(name = "mode")
  private TransportationMode mode = TransportationMode.AIR;

  /* ------------ Port/ Airport/ Location -------------- */
  @Column(name = "from_location_code")
  private String fromLocationCode;

  @Column(name = "from_location_label")
  private String fromLocationLabel;

  @Column(name = "to_location_code")
  private String toLocationCode;
  
  @Column(name = "to_location_label")
  private String toLocationLabel;
  
  @Column(name = "final_destination")
  private String finalDestination;

  /* ------------ Agent/ Carrier/ Pricing Input Rate -------------- */
  @Column(name = "handling_agent_partner_label")
  private String handlingAgentPartnerLabel;
  
  @Column(name = "handling_agent_partner_id")
  private Long handlingAgentPartnerId;

  @Column(name = "carrier_label")
  private String carrierLabel;
  
  @Column(name = "carrier_partner_id")
  private Long carrierPartnerId;

  /* ------------ Rate Info-------------- */
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "validity")
  private Date validity;
  
  @Column(name = "frequency")
  private String frequency;
  
  @Column(name = "transit_port")
  private String transitPort;
  
  @Column(name = "transit_time")
  private String transitTime;
  
  @Column(name = "note", length = 1024 * 4)
  private String note;

  /* ------------ Volume/ Weight/ Quantity -------------- */
  //TODO: Dan - consider to remove this field, follow by inquiry
  // for sea lcl freight
  @Column(name = "volume_in_cbm")
  private double volumeInCbm;

  //TODO: Dan - consider to remove this field, follow by inquiry
  // for air freight
  @Column(name = "chargeable_weight")
  private double chargeableWeight;

  /* ------------ Price Info -------------- */
  @Column(name = "ref_currency")
  private String refCurrency;
  
  @Column(name = "currency")
  private String currency;
  
  @Transient
  private MapObject priceGroup = new MapObject();

  @JsonIgnore
  @Access(AccessType.PROPERTY)
  @Basic(fetch = FetchType.EAGER)
  @Column(name = "price_group", length = 64 * 1024)
  public String getPriceGroupJson() {
    if (this.priceGroup == null) return null;
    return DataSerializer.JSON.toString(this.priceGroup);
  }

  public void setPriceGroupJson(String json) {
    if (StringUtil.isEmpty(json)) {
      this.priceGroup = null;
    } else {
      this.priceGroup = DataSerializer.JSON.fromString(json, new TypeReference<>() {
      });
    }
  }

  public String getCarrierLabel() {
    if(carrierLabel == null) return "";
    return carrierLabel;
  }

  public boolean isConfirm() {
    if(isConfirm == null) return false;
    return isConfirm;
  }

  public boolean getConfirm() {
    if(isConfirm == null) return false;
    return isConfirm;
  }

  public void setConfirm(Boolean confirm) {
    if(confirm == null) this.isConfirm = false;
    isConfirm = confirm;
  }

  public QuotationCharge(SeaLclTransportCharge price) {
    referenceCode = price.getCode();
    mode = TransportationMode.SEA_LCL;
    purpose = price.getPurpose();

    handlingAgentPartnerLabel = price.getHandlingAgentPartnerLabel();
    handlingAgentPartnerId = price.getHandlingAgentPartnerId();
    carrierLabel = price.getCarrierLabel();
    carrierPartnerId = price.getCarrierPartnerId();
    refCurrency = price.getCurrency();
    currency = price.getCurrency();
    validity = price.getValidTo();

    fromLocationCode = price.getFromLocationCode();
    fromLocationLabel = price.getFromLocationLabel();
    toLocationCode = price.getToLocationCode();
    toLocationLabel = price.getToLocationLabel();

    note = Stream.of(
        Optional.ofNullable(price.getNote())
          .filter(StringUtil::isNotEmpty)
          .map(note -> note + "\n")
          .orElse(""),
        Optional.ofNullable(price.getStuffingNote())
          .filter(StringUtil::isNotEmpty)
          .map(note -> " Stuffing:\n" + note + "\n")
          .orElse(""),
        Optional.ofNullable(price.getSurchargeNote())
          .filter(StringUtil::isNotEmpty)
          .map(note -> " Surcharge:\n" + note + "\n")
          .orElse(""),
        Optional.ofNullable(price.getLocalChargeAtDest())
          .filter(StringUtil::isNotEmpty)
          .map(note -> " Local Charge at Dest:\n" + note + "\n")
          .orElse("")
      )
      .filter(StringUtil::isNotEmpty)
      .collect(Collectors.joining(""));

    List<TransportFrequency> frequencies = price.getTransportFrequencies();
    if (net.datatp.util.ds.Collections.isNotEmpty(frequencies)) {
      frequency = frequencies.get(0).getLabel();

      transitTime = StringUtil.join(new String[]{
        String.valueOf(frequencies.get(0).getTransitTime()),
        String.valueOf(frequencies.get(0).getTransitLabel())
      }, " ").trim();

    }

    volumeInCbm = 0;
    priceGroup = new CustomerSeaLCLPriceGroup(price.getPriceGroup(), 0).toMapObject();
  }

  public QuotationCharge(SeaFclTransportCharge price) {
    referenceCode = price.getCode();
    mode = TransportationMode.SEA_FCL;
    purpose = price.getPurpose();

    handlingAgentPartnerLabel = price.getHandlingAgentPartnerLabel();
    handlingAgentPartnerId = price.getHandlingAgentPartnerId();
    carrierLabel = price.getCarrierLabel();
    carrierPartnerId = price.getCarrierPartnerId();
    refCurrency = price.getCurrency();
    currency = price.getCurrency();
    validity = price.getValidTo();

    fromLocationCode = price.getFromLocationCode();
    fromLocationLabel = price.getFromLocationLabel();
    toLocationCode = price.getToLocationCode();
    toLocationLabel = price.getToLocationLabel();
    finalDestination = price.getFinalTerminalLocationLabel();

    note = Stream.of(
        Optional.ofNullable(price.getNote())
          .filter(StringUtil::isNotEmpty)
          .map(note -> note + "\n")
          .orElse(""),
        Optional.ofNullable(price.getRemarks())
          .filter(StringUtil::isNotEmpty)
          .map(note -> " Remarks:\n" + note + "\n")
          .orElse(""),
        Optional.ofNullable(price.getSurchargeNote())
          .filter(StringUtil::isNotEmpty)
          .map(note -> " Surcharge:\n" + note + "\n")
          .orElse(""),
        Optional.ofNullable(price.getFreeTime())
          .filter(StringUtil::isNotEmpty)
          .map(note -> " Free Time:\n" + note + "\n")
          .orElse("")
      )
      .filter(StringUtil::isNotEmpty)
      .collect(Collectors.joining(""));

    List<TransportFrequency> frequencies = price.getTransportFrequencies();
    if (net.datatp.util.ds.Collections.isNotEmpty(frequencies)) {
      frequency = frequencies.get(0).getLabel();
      transitTime = StringUtil.join(new String[]{
        String.valueOf(frequencies.get(0).getTransitTime()),
        String.valueOf(frequencies.get(0).getTransitLabel())
      }, " ").trim();
    }

    priceGroup = new CustomerSeaFCLPriceGroup(price.getPriceGroup()).toMapObject();
  }

  public QuotationCharge(AirTransportCharge price) {
    referenceCode = price.getCode();
    mode = TransportationMode.AIR;
    purpose = price.getPurpose();

    handlingAgentPartnerLabel = price.getHandlingAgentPartnerLabel();
    handlingAgentPartnerId = price.getHandlingAgentPartnerId();
    carrierLabel = price.getCarrierLabel();
    carrierPartnerId = price.getCarrierPartnerId();
    refCurrency = price.getCurrency();
    currency = price.getCurrency();
    validity = price.getValidTo();

    fromLocationCode = price.getFromLocationCode();
    fromLocationLabel = price.getFromLocationLabel();
    toLocationCode = price.getToLocationCode();
    toLocationLabel = price.getToLocationLabel();

    note = Stream.of(
      Optional.ofNullable(price.getNote())
          .filter(StringUtil::isNotEmpty)
          .map(note -> note + "\n")
          .orElse(""))
      .filter(StringUtil::isNotEmpty)
      .collect(Collectors.joining(""));

    List<TransportFrequency> frequencies = price.getTransportFrequencies();
    if (net.datatp.util.ds.Collections.isNotEmpty(frequencies)) {
      frequency = frequencies.get(0).getLabel();
      transitTime = StringUtil.join(new String[]{
        String.valueOf(frequencies.get(0).getTransitTime()),
        String.valueOf(frequencies.get(0).getTransitLabel())
      }, " ").trim();
    }

    priceGroup = new CustomerAirPriceGroup(price.getPriceGroup()).toMapObject();
  }

  public QuotationCharge(QuotationSeaTransportCharge seaCharge) {
    id = seaCharge.getId();
    referenceCode = seaCharge.getSeaTransportChargeCode();
    purpose = seaCharge.getPurpose();

    if(CustomerSeaTransportCharge.SeaType.FCL.equals(seaCharge.getType())) {
      mode = TransportationMode.SEA_FCL;
      CustomerSeaFCLPriceGroup priceGroup = seaCharge.getFclPriceGroup();
      if (priceGroup == null) priceGroup = new CustomerSeaFCLPriceGroup();
      this.priceGroup = priceGroup.toMapObject();
    } else {
      mode = TransportationMode.SEA_LCL;
      volumeInCbm = seaCharge.getChargeableVolumeInCBM();
      CustomerSeaLCLPriceGroup priceGroup = seaCharge.getLclPriceGroup();
      if (priceGroup == null) priceGroup = new CustomerSeaLCLPriceGroup();
      this.priceGroup = priceGroup.toMapObject();
    }

    handlingAgentPartnerLabel = seaCharge.getHandlingAgentPartnerLabel();
    carrierLabel = seaCharge.getCarrierLabel();
    carrierPartnerId = seaCharge.getCarrierPartnerId();

    refCurrency = seaCharge.getRefCurrency();
    currency = seaCharge.getCurrency();
    validity = seaCharge.getValidTo();
    note = seaCharge.getNote();

    fromLocationCode = seaCharge.getFromLocationCode();
    fromLocationLabel = seaCharge.getFromLocationLabel();
    toLocationCode = seaCharge.getToLocationCode();
    toLocationLabel = seaCharge.getToLocationLabel();

    final List<TransportFrequency> frequencies = seaCharge.getTransportFrequencies();
    if (Objects.nonNull(frequencies) && !frequencies.isEmpty()) {
      frequency = frequencies.get(0).getLabel();
      if (StringUtil.isNotEmpty(frequencies.get(0).getTransitLabel())) {
        transitTime = StringUtil.join(new String[]{
          String.valueOf(frequencies.get(0).getTransitTime()),
          String.valueOf(frequencies.get(0).getTransitLabel())
        }, " ").trim();
      } else {
        transitTime = String.valueOf(frequencies.get(0).getTransitTime());
      }
    }
  }

  /* ----------------- Air Freight --------------- */
  public QuotationCharge(QuotationAirTransportCharge charge) {
    id = charge.getId();
    referenceCode = charge.getAirTransportChargeCode();
    chargeableWeight = charge.getChargeableWeightInKG();
    purpose = charge.getPurpose();
    carrierLabel = charge.getCarrierLabel();
    carrierPartnerId = charge.getCarrierPartnerId();
    handlingAgentPartnerLabel = charge.getHandlingAgentPartnerLabel();
    refCurrency = charge.getRefCurrency();
    currency = charge.getCurrency();
    validity = charge.getValidTo();
    note = charge.getNote();

    final List<TransportFrequency> frequencies = charge.getTransportFrequencies();
    if (Objects.nonNull(frequencies) && !frequencies.isEmpty()) {
      frequency = frequencies.get(0).getLabel();
      transitTime = StringUtil.join(new String[]{
        String.valueOf(frequencies.get(0).getTransitTime()),
        String.valueOf(frequencies.get(0).getTransitLabel())
      }, " ").trim();

    }

    CustomerAirPriceGroup priceGroup = charge.getPriceGroup();
    if (priceGroup == null) priceGroup = new CustomerAirPriceGroup();
    this.priceGroup = priceGroup.toMapObject();
  }

  public QuotationCharge computeFromInquiry(SpecificServiceInquiry inquiry) {
    if (inquiry == null) return this;
    fromLocationLabel = inquiry.getFromLocationLabel();
    fromLocationCode = inquiry.getFromLocationCode();
    toLocationLabel = inquiry.getToLocationLabel();
    toLocationCode = inquiry.getToLocationCode();
    finalDestination = inquiry.getFinalDestination();
    return this;
  }

}