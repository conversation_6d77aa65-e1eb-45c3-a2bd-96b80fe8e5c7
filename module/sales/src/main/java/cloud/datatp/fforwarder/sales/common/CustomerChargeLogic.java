package cloud.datatp.fforwarder.sales.common;

import cloud.datatp.fforwarder.price.AirPriceLogic;
import cloud.datatp.fforwarder.price.SeaPriceLogic;
import cloud.datatp.fforwarder.price.TruckPriceLogic;
import cloud.datatp.fforwarder.price.common.ContainerPriceGroup;
import cloud.datatp.fforwarder.price.common.TruckPriceGroup;
import cloud.datatp.fforwarder.price.entity.AirTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaLclTransportCharge;
import cloud.datatp.fforwarder.price.entity.TruckContainerTransportCharge;
import cloud.datatp.fforwarder.price.entity.TruckRegularTransportCharge;
import cloud.datatp.fforwarder.sales.booking.dto.BookingModel;
import cloud.datatp.fforwarder.sales.booking.entity.BookingRailTransportCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.QuotationAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.QuotationCharge;
import cloud.datatp.fforwarder.sales.common.repository.BookingAirTransportChargeRepository;
import cloud.datatp.fforwarder.sales.common.repository.BookingRailTransportChargeRepository;
import cloud.datatp.fforwarder.sales.common.repository.QuotationAdditionalChargeRepository;
import cloud.datatp.fforwarder.sales.common.repository.QuotationAirTransportChargeRepository;
import cloud.datatp.fforwarder.sales.common.repository.QuotationChargeRepository;
import cloud.datatp.fforwarder.sales.common.repository.QuotationRailTransportChargeRepository;
import cloud.datatp.fforwarder.sales.common.repository.QuotationSeaTransportChargeRepository;
import cloud.datatp.fforwarder.sales.quotation.SpecificQuotationLogic;
import cloud.datatp.fforwarder.sales.quotation.dto.LocalCharge;
import cloud.datatp.fforwarder.sales.quotation.dto.QuotationDomesticsChargeModel;
import cloud.datatp.fforwarder.sales.quotation.entity.QuotationRailTransportCharge;
import cloud.datatp.fforwarder.settings.TransportationMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CustomerChargeLogic {

  @Autowired
  private QuotationChargeRepository quotationChargeRepo;

  @Autowired
  private QuotationAdditionalChargeRepository quotationAddChargeRepo;

  @Autowired
  private AirPriceLogic airPriceLogic;

  @Autowired
  private SeaPriceLogic seaPriceLogic;

  @Autowired
  private TruckPriceLogic truckChargeLogic;

  @Autowired
  private SpecificQuotationLogic sQuotationLogic;

  @Autowired
  private BookingAirTransportChargeRepository airRepo;

  @Autowired
  private BookingRailTransportChargeRepository railRepo;

  @Autowired
  private QuotationAirTransportChargeRepository quotationAirRepo;

  @Autowired
  private QuotationSeaTransportChargeRepository quotationSeaRepo;

  @Autowired
  private QuotationRailTransportChargeRepository quotationRailRepo;

  public void updateFeedbackPrice(ClientInfo client, Company company, Long sQuotationChargeId, BookingModel model) {
    if(sQuotationChargeId != null) {
      Optional<QuotationCharge> chargeOptional = quotationChargeRepo.findById(sQuotationChargeId);
      if(chargeOptional.isPresent() && TransportationMode.isSeaFCLTransport(chargeOptional.get().getMode())) {
        SeaFclTransportCharge price = seaPriceLogic.getSeaFclPriceByCode(client, company, (chargeOptional.get().getReferenceCode()));
        List<MapObject> feedbacks = price.getFeedbacks();
        if(feedbacks == null) feedbacks = new ArrayList<>();
        MapObject newFeedback = new MapObject();
        newFeedback.put("salemanAccountId", model.getSenderAccountId());
        newFeedback.put("salemanLabel", model.getSenderLabel());
        newFeedback.put("feedbackDate", DateUtil.asCompactDateTime(model.getInquiry().getRequestDate()));
        if(StringUtil.isNotEmpty(model.getUnitOfPackage())) {
          newFeedback.put("feedback", "Win: " + model.getInquiry().getIncoterms() + " " + model.getUnitOfPackage());
        } else {
          newFeedback.put("feedback", "Win: " + model.getInquiry().getIncoterms() + " " + model.getGrossWeight() + " KGS, " + model.getVolume() + " CBM" );
        }
        feedbacks.add(newFeedback);
        price.setFeedbacks(feedbacks);
        seaPriceLogic.saveSeaFclTransportCharge(client, company, price);
      }
    }
  }

  public List<QuotationCharge> findQuotationChargeBySQuotationId(Company company, Long sQuotationId) {
    return quotationChargeRepo.findBySQuotationId(company.getId(), sQuotationId);
  }

  public QuotationCharge saveQuotationCharge(ClientInfo client, Company company, QuotationCharge charge) {
    charge.set(client, company);
    return quotationChargeRepo.save(charge);
  }

  public List<QuotationCharge> saveQuotationCharges(
    ClientInfo client, Company company, Long sQuotationId, List<QuotationCharge> charges) {
    for(QuotationCharge charge : charges) {
      charge.setSpecificQuotationId(sQuotationId);
      charge.set(client, company);
    }
    return quotationChargeRepo.saveAll(charges);
  }

  public Boolean deleteQuotationCharge(ClientInfo client, Company company, Long sQuotationId) {
    return quotationChargeRepo.deleteBySQuotationId(sQuotationId) > 0;
  }

  public List<QuotationAdditionalCharge> findAddChargeBySQuotationId(Company company, Long sQuotationId) {
    return quotationAddChargeRepo.findBySQuotationId(company.getId(), sQuotationId);
  }

  public QuotationAdditionalCharge saveAdditionalCharge(ClientInfo client, Company company, QuotationAdditionalCharge charge) {
    charge.set(client, company);
    return quotationAddChargeRepo.save(charge);
  }

  public List<QuotationAdditionalCharge> saveAdditionalCharges(
    ClientInfo client, Company company, Long sQuotationId, List<QuotationAdditionalCharge> charges) {
    for(QuotationAdditionalCharge charge : charges) {
      charge.setSpecificQuotationId(sQuotationId);
      charge.set(client, company);
    }
    return quotationAddChargeRepo.saveAll(charges);
  }

  public Boolean deleteAdditionalCharge(ClientInfo client, Company company, Long sQuotationId) {
    return quotationAddChargeRepo.deleteBySQuotationId(sQuotationId) > 0;
  }

  public BookingRailTransportCharge getBookingRailTransportChargeById(Long id) {
    return railRepo.getById(id);
  }

  public BookingRailTransportCharge saveBookingRailCharge(
    ClientInfo client, Company company, BookingRailTransportCharge charge) {
    charge.set(client, company);
    return railRepo.save(charge);
  }

  public List<QuotationRailTransportCharge> findQuotationRailChargesByCompany(ClientInfo client, Company company) {
    return quotationRailRepo.findByCompanyId(company.getId());
  }

  public QuotationRailTransportCharge saveQuotationRailTransportCharge(ClientInfo client, Company company, QuotationRailTransportCharge entity) {
    entity.set(client, company);
    return quotationRailRepo.save(entity);
  }

  /* ----------------- Customer Charge------------------ */
  public List<QuotationCharge> createAirQuotes(ClientInfo client, Company company, List<Long> airPriceIds) {
    List<AirTransportCharge> foundPrices = airPriceLogic.findByIds(client, company, airPriceIds);
    return foundPrices.stream().map(QuotationCharge::new).collect(Collectors.toList());
  }

  public List<QuotationCharge> createSeaQuotesFCL(ClientInfo client, Company company, List<Long> seaFclPriceIds) {
    List<SeaFclTransportCharge> foundSeaCharges = seaPriceLogic.findSeaFclTransportChargeByIds(client, company, seaFclPriceIds);
    return foundSeaCharges.stream().map(QuotationCharge::new).collect(Collectors.toList());
  }

  public List<QuotationCharge> createSeaQuotesLCL(ClientInfo client, Company company, List<Long> seaLclPriceIds) {
    List<SeaLclTransportCharge> foundSeaCharges = seaPriceLogic.findSeaLclTransportChargeByIds(client, company, seaLclPriceIds);
    return foundSeaCharges.stream().map(QuotationCharge::new).collect(Collectors.toList());
  }

  public QuotationDomesticsChargeModel createTruckQuote(ClientInfo client, Company company, List<Long> truckPriceIds) {

    List<CustomerTruckTransportAdditionalCharge> addCharges = new ArrayList<>();
    List<TruckRegularTransportCharge> foundPrices = truckChargeLogic.findTruckByIds(client, company, truckPriceIds);
    for (TruckRegularTransportCharge foundPrice : foundPrices) {
      TruckPriceGroup priceGroup = foundPrice.getPriceGroup();
      if (priceGroup == null) continue;

      CustomerTruckTransportAdditionalCharge truck1_25T = new CustomerTruckTransportAdditionalCharge();
      truck1_25T.setName("S_TRUCK");
      truck1_25T.setLabel("DOMESTIC TRUCKING FEE");
      truck1_25T.setQuantity(1);
      truck1_25T.setUnit("1.25T");
      truck1_25T.setCurrency("VND");
      truck1_25T.setUnitPrice(priceGroup.getTruck1Ton25Price());
      truck1_25T.setTotal(priceGroup.getTruck1Ton25Price());
      truck1_25T.setFinalCharge(priceGroup.getTruck1Ton25Price());
      truck1_25T.setNote("Truck 1.25Ton");
      addCharges.add(truck1_25T);

      CustomerTruckTransportAdditionalCharge truck1_5T = DataSerializer.JSON.clone(truck1_25T);
      truck1_5T.setUnit("1.5T");
      truck1_5T.setUnitPrice(priceGroup.getTruck1Ton5Price());
      truck1_5T.setTotal(priceGroup.getTruck1Ton5Price());
      truck1_5T.setFinalCharge(priceGroup.getTruck1Ton5Price());
      truck1_5T.setNote("Truck 1.5Ton");
      if (truck1_5T.getUnitPrice() > 0) addCharges.add(truck1_5T);

      CustomerTruckTransportAdditionalCharge truck2_5T = DataSerializer.JSON.clone(truck1_25T);
      truck2_5T.setUnit("2.5T");
      truck2_5T.setUnitPrice(priceGroup.getTruck2Ton5Price());
      truck2_5T.setTotal(priceGroup.getTruck2Ton5Price());
      truck2_5T.setFinalCharge(priceGroup.getTruck2Ton5Price());
      truck2_5T.setNote("Truck 2.5Ton");
      if (truck2_5T.getUnitPrice() > 0) addCharges.add(truck2_5T);

      CustomerTruckTransportAdditionalCharge truck3_5T = DataSerializer.JSON.clone(truck1_25T);
      truck3_5T.setUnit("3.5T");
      truck3_5T.setUnitPrice(priceGroup.getTruck3Ton5Price());
      truck3_5T.setTotal(priceGroup.getTruck3Ton5Price());
      truck3_5T.setFinalCharge(priceGroup.getTruck3Ton5Price());
      truck3_5T.setNote("Truck 3.5Ton");
      if (truck3_5T.getUnitPrice() > 0) addCharges.add(truck3_5T);

      CustomerTruckTransportAdditionalCharge truck5T = DataSerializer.JSON.clone(truck1_25T);
      truck5T.setUnit("5T");
      truck5T.setUnitPrice(priceGroup.getTruck5TonPrice());
      truck5T.setTotal(priceGroup.getTruck5TonPrice());
      truck5T.setFinalCharge(priceGroup.getTruck5TonPrice());
      truck5T.setNote("Truck 5Ton");
      if (truck5T.getUnitPrice() > 0) addCharges.add(truck5T);

      CustomerTruckTransportAdditionalCharge truck7T = DataSerializer.JSON.clone(truck1_25T);
      truck7T.setUnit("7T");
      truck7T.setUnitPrice(priceGroup.getTruck7TonPrice());
      truck7T.setTotal(priceGroup.getTruck7TonPrice());
      truck7T.setFinalCharge(priceGroup.getTruck7TonPrice());
      truck7T.setNote("Truck 7Ton");
      if (truck7T.getUnitPrice() > 0) addCharges.add(truck7T);

      CustomerTruckTransportAdditionalCharge truck8T = DataSerializer.JSON.clone(truck1_25T);
      truck8T.setUnit("8T");
      truck8T.setUnitPrice(priceGroup.getTruck8TonPrice());
      truck8T.setTotal(priceGroup.getTruck8TonPrice());
      truck8T.setFinalCharge(priceGroup.getTruck8TonPrice());
      truck8T.setNote("Truck 8Ton");
      if (truck8T.getUnitPrice() > 0) addCharges.add(truck8T);

      CustomerTruckTransportAdditionalCharge truck10T = DataSerializer.JSON.clone(truck1_25T);
      truck10T.setUnit("10T");
      truck10T.setUnitPrice(priceGroup.getTruck10TonPrice());
      truck10T.setTotal(priceGroup.getTruck10TonPrice());
      truck10T.setFinalCharge(priceGroup.getTruck10TonPrice());
      truck10T.setNote("Truck 10Ton");
      if (truck10T.getUnitPrice() > 0) addCharges.add(truck10T);
    }

    return new QuotationDomesticsChargeModel(LocalCharge.convertToLocalCharge(addCharges));
  }

  public QuotationDomesticsChargeModel createContainerQuotes(ClientInfo client, Company company, List<Long> containerPriceIds) {
    List<CustomerTruckTransportAdditionalCharge> addCharges = new ArrayList<>();

    List<TruckContainerTransportCharge> foundPrices = truckChargeLogic.findContainerByIds(client, company, containerPriceIds);
    for (TruckContainerTransportCharge foundPrice : foundPrices) {
      ContainerPriceGroup priceGroup = foundPrice.getPriceGroup();
      if (priceGroup == null) continue;

      CustomerTruckTransportAdditionalCharge cont20DC = new CustomerTruckTransportAdditionalCharge();
      cont20DC.setName("S_TRUCK");
      cont20DC.setLabel("DOMESTIC TRUCKING FEE");
      cont20DC.setQuantity(1);
      cont20DC.setUnit(ContainerType.ContainerTypeUnit._20DC.getName());
      cont20DC.setCurrency("VND");
      cont20DC.setUnitPrice(priceGroup.getContDry20Lt10TonPrice());
      cont20DC.setTotal(priceGroup.getContDry20Lt10TonPrice());
      cont20DC.setFinalCharge(priceGroup.getContDry20Lt10TonPrice());
      cont20DC.setNote("Cont 20' < 10Ton");
      addCharges.add(cont20DC);

      CustomerTruckTransportAdditionalCharge cont20DCGEQ = DataSerializer.JSON.clone(cont20DC);
      cont20DCGEQ.setUnit(ContainerType.ContainerTypeUnit._20DC.getName());
      cont20DCGEQ.setUnitPrice(priceGroup.getContDry20Geq17TonPrice());
      cont20DCGEQ.setTotal(priceGroup.getContDry20Geq17TonPrice());
      cont20DCGEQ.setFinalCharge(priceGroup.getContDry20Geq17TonPrice());
      cont20DCGEQ.setNote("Cont 20' > 17Ton");
      if (cont20DCGEQ.getUnitPrice() > 0) addCharges.add(cont20DCGEQ);

      CustomerTruckTransportAdditionalCharge cont40DC = DataSerializer.JSON.clone(cont20DC);
      cont40DC.setUnit(ContainerType.ContainerTypeUnit._40DC.getName());
      cont40DC.setUnitPrice(priceGroup.getContDry40Lt17TonPrice());
      cont40DC.setTotal(priceGroup.getContDry40Lt17TonPrice());
      cont40DC.setFinalCharge(priceGroup.getContDry40Lt17TonPrice());
      cont40DC.setNote("Cont 40' < 17Ton");
      if (cont40DC.getUnitPrice() > 0) addCharges.add(cont40DC);

      CustomerTruckTransportAdditionalCharge cont40DCGEQ = DataSerializer.JSON.clone(cont20DC);
      cont40DCGEQ.setUnit(ContainerType.ContainerTypeUnit._40DC.getName());
      cont40DCGEQ.setUnitPrice(priceGroup.getContDry40Geq17TonPrice());
      cont40DCGEQ.setTotal(priceGroup.getContDry40Geq17TonPrice());
      cont40DCGEQ.setFinalCharge(priceGroup.getContDry40Geq17TonPrice());
      cont40DCGEQ.setNote("Cont 40' > 17Ton");
      if (cont40DCGEQ.getUnitPrice() > 0) addCharges.add(cont40DCGEQ);
    }

    return new QuotationDomesticsChargeModel(LocalCharge.convertToLocalCharge(addCharges));
  }

}