package cloud.datatp.fforwarder.sales.partner;

import cloud.datatp.bfsone.partner.BFSOnePartnerLogic;
import cloud.datatp.fforwarder.price.common.ProvinceNormalizer;
import cloud.datatp.fforwarder.sales.partner.entity.SalemanKeyAccountReport;
import cloud.datatp.fforwarder.sales.partner.repository.SalemanKeyAccountReportRepository;
import cloud.datatp.fforwarder.sales.report.SaleReportMetricsHelper;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.core.security.entity.Capability;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.ExternalDataSourceManager;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryUnitManager;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.http.get.GETContent;
import net.datatp.module.http.get.GETTmpStoreHandler;
import net.datatp.module.http.get.StoreInfo;
import net.datatp.util.ds.Objects;
import net.datatp.util.io.IOUtil;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Component
public class PartnerReportLogic extends DAOService {

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private BFSOnePartnerLogic bfsonePartnerLogic;

  @Autowired
  private ProvinceNormalizer normalizer;

  @Autowired
  private ExternalDataSourceManager dataSourceManager;

  @Autowired
  private SalemanKeyAccountReportRepository salemanKeyAccountReportRepo;

  @Autowired
  private GETTmpStoreHandler tmpStoreHandler;

  public StoreInfo getPartnerContactById(ClientInfo client, Company company, Long partnerId) {
    try {
      InputStream inputStream = IOUtil.loadResource("classpath:data/logistics/sales/partner-contract-sample.xlsx");
      byte[] streamContentAsBytes = IOUtil.getStreamContentAsBytes(inputStream);
      GETContent getContent = new GETContent("partner-contact.xlsx", streamContentAsBytes);
      return tmpStoreHandler.store(client, getContent, true);
    } catch (Exception ex) {
      log.warn("Error when get partner contact", ex);
      return null;
    }
  }

  public List<SqlMapRecord> searchVolumeSalemanKeyAccountReport(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null) return new ArrayList<>();

    Long salemanAccountId = sqlParams.getLong("salemanAccountId");
    if (salemanAccountId == null) salemanAccountId = client.getAccountId();
    Employee employee = employeeLogic.getByAccount(client, company, salemanAccountId);
    Objects.assertNotNull(employee, "Employee not found: accountId = " + salemanAccountId);
    if (StringUtil.isEmpty(employee.getBfsoneCode())) {
      log.warn("Employee BFSOne Code is not found: login id = {}", salemanAccountId);
      return new ArrayList<>();
    }
    sqlParams.addParam("salemanContactId", employee.getBfsoneCode().trim());

    try {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/sales/groovy/report/KeyAccountReportSql.groovy";
      String scriptName = "KeyAccountVolumeReport";
      SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(bfsoneReportDataSource, sqlParams);
      List<SqlMapRecord> records = view.renameColumWithJavaConvention().getSqlMapRecords();
      log.info("Retrieved {} records", records.size());
      return records;
    } catch (Exception e) {
      log.error("Error when search Saleman Key Account Report", e);
      return new ArrayList<>();
    }
  }

  public SalemanKeyAccountReport getLatestReportBySaleman(ClientInfo client, Company company) {
    return salemanKeyAccountReportRepo.getLatestReportBySaleman(company.getId(), client.getAccountId());
  }

  public List<SalemanKeyAccountReport> findSalemanKeyAccountReports(ClientInfo client, Company company) {
    return salemanKeyAccountReportRepo.findAll(company.getId());
  }

  public List<SqlMapRecord> searchSalemanKeyAccountReports(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null) return new ArrayList<>();
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", company.getId());
    List<Long> salemanAccountIds = new ArrayList<>();

    if (permission.isGroupScope()) {
      String coreScriptDir = appEnv.addonPath("core", "groovy");
      String coreScriptFile = "net/datatp/module/hr/groovy/EmployeeSql.groovy";
      List<SqlMapRecord> accountIds = searchDbRecords(client, coreScriptDir, coreScriptFile, "FindEmployeeIdsByManagerId", sqlParams);
      salemanAccountIds = accountIds.stream()
        .map(record -> record.getLong("accountId", null))
        .collect(Collectors.toList());
    } else if (permission.isOwnerScope()) {
      salemanAccountIds.add(client.getAccountId());
    }

    sqlParams.addParam("salemanAccountIds", salemanAccountIds);
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/report/KeyAccountReportSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchKeyAccountVolumeReport", sqlParams);
  }

  public SalemanKeyAccountReport saveSalemanKeyAccountReport(ClientInfo client, Company company, SalemanKeyAccountReport report) {
    report.set(client, company);
    if (report.isNew()) {
      //DAN_20053005-1
      String code = report.generateCodePrefix(client.getRemoteUser());
      String[] split = code.split("-");
      String prefix = split[0];
      List<SalemanKeyAccountReport> reportsInDb = salemanKeyAccountReportRepo.findByCodePrefix(company.getId(), prefix);
      if (!reportsInDb.isEmpty()) {
        SalemanKeyAccountReport existingReport = reportsInDb.get(0);
        //DAN_20053005-2
        String lastRecordCode = existingReport.getCode();
        String[] lastRecordCodeSplit = lastRecordCode.split("-");
        try {
          int codeIdx = Integer.parseInt(lastRecordCodeSplit[1]);
          codeIdx++;
          code = prefix + "-" + codeIdx;
        } catch (NumberFormatException e) {
          log.warn("Last record code is not valid: {}", lastRecordCode);
        }
      }

      report.setSubmittedDate(new Date());
      report.setCode(code);
      report.set(client, company);
    }
    return salemanKeyAccountReportRepo.save(report);
  }

  public int deleteSalemanKeyAccountReports(ClientInfo client, Company company, List<Long> targetIds) {
    DBConnectionUtil connectionUtil = new DBConnectionUtil(primaryDataSource);
    DeleteGraphBuilder deleteGraphBuilder = new DeleteGraphBuilder(connectionUtil, company.getId(), SalemanKeyAccountReport.class, targetIds);
    final int count = deleteGraphBuilder.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count;
  }

  public SalemanKeyAccountReport getSalemanKeyAccountReportById(ClientInfo client, Company company, Long id) {
    return salemanKeyAccountReportRepo.findById(id).get();
  }

  public List<SqlMapRecord> searchCustomerReports(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());

    Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    sqlParams.addParam("accessEmployeeId", employee.getId());

    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null || !permission.getCapability().hasCapability(Capability.Write))
      return java.util.Collections.emptyList();
    if (!sqlParams.hasParam("space")) {
      sqlParams.addParam("space", "User");
    }

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SalesCustomerReportSql.groovy";
    List<SqlMapRecord> sqlMapRecords = searchDbRecords(client, scriptDir, scriptFile, "CustomerReportManager", sqlParams);

    Map<String, List<SqlMapRecord>> groupedRecords = sqlMapRecords.stream()
      .filter(record -> StringUtil.isNotEmpty(record.getString("bfsonePartnerCode", "")))
      .collect(Collectors.groupingBy(record -> record.getString("bfsonePartnerCode", "")));

    List<SqlMapRecord> holder = new ArrayList<>();

    if (!groupedRecords.isEmpty()) {
      String scriptDirBfs = appEnv.addonPath("logistics", "groovy");
      String scriptFileBfs = "cloud/datatp/bfsone/groovy/BFSOnePartnerSql.groovy";
      String scriptName = "BFSOnePartnerVolumeReport";

      SqlQueryParams sqlParamBfs = new SqlQueryParams();
      sqlParamBfs.addParam("partnerCodes", new ArrayList<>(groupedRecords.keySet()));
      SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDirBfs, scriptFileBfs, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(bfsoneReportDataSource, sqlParamBfs);
      List<SqlMapRecord> reportRecords = view.renameColumWithJavaConvention().getSqlMapRecords();

      Map<String, List<SqlMapRecord>> groupedReportRecords = reportRecords.stream()
        .filter(record -> StringUtil.isNotEmpty(record.getString("bfsonePartnerCode", "")))
        .collect(Collectors.groupingBy(record -> record.getString("bfsonePartnerCode", "")));

      for (String bfsonePartnerCode : groupedRecords.keySet()) {
        SqlMapRecord record = groupedRecords.get(bfsonePartnerCode).get(0);
        String province = record.getString("locationState", "");
        String normalizedProvince = normalizer.normalizeProvinceToken(province, false);
        record.put("locationState", normalizedProvince);

        List<SqlMapRecord> reportRecordList = groupedReportRecords.get(bfsonePartnerCode);
        if (reportRecordList != null && !reportRecordList.isEmpty()) {
          int totalJobCount = reportRecordList.size();

          // Sort by jobDate descending to get latest transaction
          reportRecordList.sort((a, b) -> {
            try {
              String dateStrA = a.getString("jobDate", "");
              String dateStrB = b.getString("jobDate", "");

              // Parse dates using DateUtil
              Date dateA = DateUtil.parseCompactDateTime(dateStrA);
              Date dateB = DateUtil.parseCompactDateTime(dateStrB);

              // Sort descending (most recent first)
              return dateB.compareTo(dateA);
            } catch (Exception e) {
              log.error("Error parsing date for sorting: {}", e.getMessage());
              return 0; // Keep original order if parsing fails
            }
          });

          // Get latest transaction info
          SqlMapRecord latestRecord = reportRecordList.get(0);
          String lastTransactionDate = latestRecord.getString("jobDate", "").split("@")[0];
          String lastTransactionID = latestRecord.getString("jobNo", "");

          record.put("totalJobCount", totalJobCount);
          record.put("lastTransactionDate", lastTransactionDate);
          record.put("lastTransactionID", lastTransactionID);

          SaleReportMetricsHelper.calculateAllTimeMetrics(record, reportRecordList);
        } else {
          record.put("totalJobCount", 0);
          record.put("lastTransactionDate", "");
          record.put("lastTransactionID", "");

          record.put("totalGw", 0.0);
          record.put("totalCbm", 0.0);
          record.put("totalContainer", "");

        }
        holder.add(record);
      }
    } else {
      holder = sqlMapRecords;
    }
    return holder;
  }

}