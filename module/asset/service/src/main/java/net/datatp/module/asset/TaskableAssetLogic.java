package net.datatp.module.asset;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.asset.api.AssetTaskConfirmationPlugin;
import net.datatp.module.asset.entity.TaskableAsset;
import net.datatp.module.asset.entity.TaskableAsset.AssetTaskStatus;
import net.datatp.module.asset.entity.TaskableAsset.AssetTaskType;
import net.datatp.module.asset.repository.TaskableAssetRepository;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.communication.CommunicationMessageService;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.communication.entity.MessageDeliverType;
import net.datatp.module.communication.entity.TargetRecipient;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.core.security.AuthorizedToken;
import net.datatp.module.core.security.api.ApiAuthorizationService;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.wfms.entity.EntityTask;
import net.datatp.module.wfms.entity.EntityTaskApprovalStatus;
import net.datatp.module.wfms.entity.EntityTaskRequest;
import net.datatp.module.wfms.entity.EntityTaskStatus;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;

@Component
public class TaskableAssetLogic extends DAOService {
  
  @Autowired
  private TaskableAssetRepository repo;
  
  @Autowired
  private AccountLogic accountLogic;
  
  @Autowired
  private CompanyConfigLogic companyConfigLogic;
  
  @Autowired
  private CommunicationMessageService messageService;
  
  @Autowired
  private ApiAuthorizationService apiAuthorizationService;
  
  @Autowired
  private AssetTaskPlugin taskPlugin;
  
  private String URL;
  
  @PostConstruct
  public void onInit() {
    if(appEnv.isProdEnv()) {
      URL = "https://beelogistics.cloud/";
    } else {
      URL = "http://localhost:7080/";
    }
  }
  
  public TaskableAsset getTaskableAsset(ClientInfo client, ICompany company, Long id) {
    return repo.getById(id);
  }
  
  public TaskableAsset saveTaskableAsset(ClientInfo client, ICompany company, TaskableAsset tAsset) {
    tAsset.set(client, company);
    if (tAsset.isNew() && Collections.isEmpty(tAsset.getSendToEmails())) {
      tAsset.setStatus(AssetTaskStatus.Approved);
      tAsset = repo.save(tAsset);
    } else if (tAsset.isSendEmail()) {
      tAsset = repo.save(tAsset);
      sendConfirmationEmailAssetTask(client, company, tAsset);
    } else {
      tAsset = repo.save(tAsset);
    }
    return tAsset;
  }
  
  private void sendConfirmationEmailAssetTask(ClientInfo client, ICompany company, TaskableAsset tAsset) {
    if (tAsset.getTaskType() == AssetTaskType.Car) sendConfirmationEmailCarRequest(client, company, tAsset);
    else if (tAsset.getTaskType() == AssetTaskType.MeetingRoom) sendAlertEmailMeeting(client, company, tAsset);
  }
  
  private void sendAlertEmailMeeting(ClientInfo client, ICompany company, TaskableAsset tAsset) {
    Account senderAccount = accountLogic.getAccountByEmail(client, "<EMAIL>");
    Message message = new Message(senderAccount.getLoginId());
    message.setSubject("You have new message from CRM system !");
    String content = 
        "<div>You have new Meeting/Traing alert as follows:</div>" +
            createMessageContent(client, company, tAsset);
    message.setContent(content);
 
    for (String email : tAsset.getSendToEmails()) {
      Account receiverAccount = accountLogic.getAccountByEmail(client, email);
      CommunicationAccount receiver = messageService.getCommunicationAccount(client, receiverAccount.getLoginId());
      TargetRecipient targetRecipient = new TargetRecipient(MessageDeliverType.Email, receiver.getEmail(), receiver.getFullName());
      targetRecipient.setForwardEmail(receiver.isAutoForward());
      message.withRecipient(targetRecipient);
    }
    messageService.sendMessage(client, company, message);
  }
  
  private void sendConfirmationEmailCarRequest(ClientInfo client, ICompany company, TaskableAsset tAsset) {
    Account senderAccount = accountLogic.getAccountByEmail(client, "<EMAIL>");
    for (String email : tAsset.getSendToEmails()) {
      Message message = new Message(senderAccount.getLoginId());
      message.setSubject("[" + tAsset.getTaskType() + " Request]" + tAsset.getPicAccountFullName() + ": " + tAsset.getLabel());
      Account receiverAccount = accountLogic.getAccountByEmail(client, email);
      String confirmURL = URL + "api/ui/" + createAccessToken(client, company, tAsset.getId(), receiverAccount.getId(), tAsset.getStatus().toString());
      String content = 
          "<div>You have new " + tAsset.getTaskType().toString() + " requirement alert as follows:</div>" +
              createMessageContent(client, company, tAsset) +
              appendButton("Click Here to Approve", confirmURL, "#0d6efd") +
              "<div><i>(Please do not reply to this email, because it was sent automatically by the system).</i></div>";
      message.setContent(content);
     
      CommunicationAccount receiver = messageService.getCommunicationAccount(client, receiverAccount.getLoginId());
      TargetRecipient targetRecipient = new TargetRecipient(MessageDeliverType.Email, receiver.getEmail(), receiver.getFullName());
      targetRecipient.setForwardEmail(receiver.isAutoForward());
      message.withRecipient(targetRecipient);
      messageService.sendMessage(client, company, message);
    }
  }
  
  public void sendConfirmationResultEmailAssetTask(ClientInfo client, ICompany company, TaskableAsset tAsset, Long approverAccountId) {
    Account senderAccount = accountLogic.getAccountByEmail(client, "<EMAIL>");
    Account approverAccount = accountLogic.getAccountById(client, approverAccountId);
    Account receiverAccount = accountLogic.getAccountById(client, tAsset.getPicAccountId());
    Message message = new Message(senderAccount.getLoginId());

    message.setSubject("[" + tAsset.getTaskType() + " Request " + tAsset.getStatus() + " ]: " + tAsset.getLabel());
    
    String content = 
        "<div>Your " + tAsset.getTaskType().toString() + " requirement has " + tAsset.getStatus() + "!</div>" +
        createMessageContent(client, company, tAsset) +
        appendContent("- Approved by", approverAccount.getFullName() + " (" + approverAccount.getEmail() + ")") +
        "<div><i>(Please do not reply to this email, because it was sent automatically by the system).</i></div>";
    message.setContent(content);
    
    CommunicationAccount receiver = messageService.getCommunicationAccount(client, receiverAccount.getLoginId());
    TargetRecipient targetRecipient = new TargetRecipient(MessageDeliverType.Email, receiver.getEmail(), receiver.getFullName());
    targetRecipient.setForwardEmail(receiver.isAutoForward());
    message.withRecipient(targetRecipient);
    messageService.sendMessage(client, company, message);
  }
  
  private String createMessageContent(ClientInfo client, ICompany company, TaskableAsset tAsset) {
    Account picAccount = accountLogic.getAccountById(client, tAsset.getPicAccountId());
    
    LocalDateTime ldtUsingDate = tAsset.getUsingDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    LocalDateTime ldtFromTime = tAsset.getFromTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    LocalDateTime ldtToTime = tAsset.getToTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    LocalDateTime ldtCreated = tAsset.getCreatedTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    String usingDateStr = ldtUsingDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
    String fromTime = ldtFromTime.format(DateTimeFormatter.ofPattern("HH:mm"));
    String toTime = ldtToTime.format(DateTimeFormatter.ofPattern("HH:mm"));
    String createdTimeStr = ldtCreated.format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm"));
    
    if (tAsset.getTaskType() == AssetTaskType.MeetingRoom) {
      String content = 
          appendContent("- Meeting/Training Date", usingDateStr) +
          appendContent("- From", fromTime) +
          appendContent("- To", toTime) +
          appendContent("- Zoom ID/PW", "") +
          appendContent("- Contents", tAsset.getLabel()) +
          appendContent("- Room Name", tAsset.getAssetLabel()) +
          appendContent("- Hosted by", tAsset.getPicAccountFullName() + " (" + picAccount.getEmail() + ")") +
          appendContent("- Notes", tAsset.getDescription() != null ? tAsset.getDescription() : "") +
          appendContent("- Created by", tAsset.getCreatedByAccountFullName() + " - " + createdTimeStr) + 
          "<div><i>(Please do not reply to this email, because it was sent automatically by the system).</i></div>";
      return content;
    }
    
    String content = 
        appendContent("- Requirement Date", usingDateStr) +
        appendContent("- From", fromTime) +
        appendContent("- To", toTime) +
        appendContent("- Contents", tAsset.getLabel()) +
        appendContent("- Requested by", tAsset.getPicAccountFullName() + " (" + picAccount.getEmail() + ")") +
        appendContent("- " + tAsset.getTaskType().toString(), tAsset.getAssetLabel()) +
        appendContent("- Notes", tAsset.getDescription() != null ? tAsset.getDescription() : "") +
        appendContent("- Created by", tAsset.getCreatedByAccountFullName() + " - " + createdTimeStr);
    return content;
  }
  
  private String createAccessToken(ClientInfo client, ICompany company, Long taskableAssetId, Long receiverAccountId, String status) {
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    Long tokenId = companyConfig.getAttributeAsLong(TaskableAsset.TASKABLE_ASSET_TOKEN_ID, 55438L);

    AuthorizedToken token = new AuthorizedToken();
    token.setResourceHandler(AssetTaskConfirmationPlugin.NAME);
    token.withAllowedResourceId("taskableAssetId", taskableAssetId);
    token.withAllowedResourceId("receiverAccountId", receiverAccountId);
    return apiAuthorizationService.createAuthorizedToken(client, company, tokenId, token);
  }
  
  private String appendContent(String name, String label) {
    if(label == null) return "";
    return  "<div>" + name + ": " + label + "</div>";
  }
  
  private String appendButton(String label, String url, String color) {
    return 
        "<p style='font-size: 15px; color: #374151; line-height: 1.5; background-color: " + color + "; padding: 6px; width: 150px; text-align: center; border-radius: 8px; margin: 16px 0;'>" +
        "<a href='" + url + "'style='color: #FFFFFF; text-decoration: none; font-weight: 500;' target='_self'>" + label + "</a>" +
        "</p>";
  }
  
  public TaskableAsset handleTaskableAsset(ClientInfo client, ICompany company, TaskableAsset tAsset) {
    EntityTask entityTask = new EntityTask();
    if (!tAsset.isNew()) {
      List<EntityTask> entityTasks = taskPlugin.findByEntityId(client, company, TaskableAsset.TABLE_NAME, tAsset.getId());
      if (Collections.isNotEmpty(entityTasks)) entityTask = entityTasks.get(0);
    } else {
      tAsset.setEntityTaskRequest(new EntityTaskRequest());
    }

    computeEntityTaskData(entityTask, tAsset);
    taskPlugin.handle(client, company, tAsset, entityTask, EntityTaskStatus.Submitted);

    return tAsset;
  }
  
  private void computeEntityTaskData(EntityTask entityTask, TaskableAsset tAsset) {
    if (StringUtil.isNotEmpty(tAsset.getLabel())) {
      entityTask.setLabel(tAsset.getLabel());
    } else {
      entityTask.setLabel("N/A");
    }

    entityTask.setStatus(EntityTaskStatus.Submitted);
    entityTask.setApprovalStatus(EntityTaskApprovalStatus.Approved);
    entityTask.setTaskType("ASSET");
    entityTask.setDueDate(tAsset.getFromTime());
    entityTask.setDeadline(tAsset.getToTime());
    entityTask.setAssigneeAccountId(tAsset.getPicAccountId());
    entityTask.setAssigneeFullName(tAsset.getPicAccountFullName());
    entityTask.setReporterAccountId(tAsset.getPicAccountId());
    entityTask.setReporterFullName(tAsset.getPicAccountFullName());
  }
  
  public TaskableAsset updateAssetTaskStatus(ClientInfo client, ICompany company, Long taskableAssetId, AssetTaskStatus status) {
    TaskableAsset taskableAsset = getTaskableAsset(client, company, taskableAssetId);
    taskableAsset.setStatus(status);

    handleTaskableAsset(client, company, taskableAsset);
    return taskableAsset;
  }
  
  public TaskableAsset computeSendToEmails(ClientInfo client, ICompany company, TaskableAsset taskableAsset) {
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    String emailsToken = null;
    if (taskableAsset.getTaskType() == AssetTaskType.Car) {
      emailsToken = companyConfig.getAttributeAsString(TaskableAsset.TASKABLE_ASSET_CAR_NOTI_EMAILS, null);
    } else if (taskableAsset.getTaskType() == AssetTaskType.MeetingRoom) {
      emailsToken = companyConfig.getAttributeAsString(TaskableAsset.TASKABLE_ASSET_MEETING_ROOM_NOTI_EMAILS, null);
    } else {
      emailsToken = companyConfig.getAttributeAsString(TaskableAsset.TASKABLE_ASSET_OTHER_NOTI_EMAILS, null);
    }
    
    taskableAsset.setSendToEmails(new HashSet<>());
    
    if (StringUtil.isNotEmpty(emailsToken)) {
      String[] emails = emailsToken.split(";");
      for (String email : emails) {
        Account account = accountLogic.getAccountByEmail(client, email.trim());
        Objects.assertNotNull(account, "Account is not found by Email: {}", email.trim());
        taskableAsset.getSendToEmails().add(email.trim());
      }
    }
    
    return taskableAsset;
  }

  public TaskableAsset initTaskableAsset(ClientInfo client, ICompany company, TaskableAsset tAsset) {
    tAsset = Objects.ensureNotNull(tAsset, TaskableAsset::new);
    
    Account clientAccount = accountLogic.getAccountByLoginId(client, client.getRemoteUser());
    tAsset.setPicAccountId(clientAccount.getId());
    tAsset.setPicAccountFullName(clientAccount.getFullName());
    tAsset.setCreatedByAccountId(clientAccount.getId());
    tAsset.setCreatedByAccountFullName(clientAccount.getFullName());
    tAsset.setTaskType(AssetTaskType.Car);
    tAsset.setSendEmail(true);
    tAsset = computeSendToEmails(client, company, tAsset);
    
    if (tAsset.getUsingDate() == null) {
      LocalDateTime now = LocalDateTime.now();
      tAsset.setUsingDate(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
      tAsset.setFromTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
      tAsset.setToTime(Date.from(now.plusHours(1).atZone(ZoneId.systemDefault()).toInstant()));
    }
    
    return tAsset;
  }
  
  public List<TaskableAsset> findUsedAssets(ClientInfo client, ICompany company, FindAssetTimeConflictParams params) {
    return repo.findUsedAssets(params.getAssetId(), params.getFromTime(), params.getToTime());
  }
  
  public List<SqlMapRecord> searchTaskableAssets(ClientInfo client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir  = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/asset/groovy/AssetSql.groovy";
    sqlParams.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchTaskableAssets", sqlParams);
  }
  
  public List<SqlMapRecord> searchAssetTasks(ClientInfo client, ICompany company, SqlQueryParams sqlParams) {
    List<SqlMapRecord> taskableAsset = searchTaskableAssets(client, company, sqlParams);
    List<Long> ids = taskableAsset.stream().map(record -> record.getLong("id")).collect(Collectors.toList());
    
    if (Collections.isEmpty(ids)) return new ArrayList<>();
    else sqlParams.addParam("entityRefIds", ids);
    
    String scriptDir  = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/wfms/groovy/EntityTaskSql.groovy";
    sqlParams.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchEntityTask", sqlParams);
  }
}
