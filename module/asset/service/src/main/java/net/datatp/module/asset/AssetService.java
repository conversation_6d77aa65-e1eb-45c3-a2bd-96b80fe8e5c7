package net.datatp.module.asset;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.Getter;
import net.datatp.module.asset.entity.Asset;
import net.datatp.module.asset.entity.AssetResponsibility;
import net.datatp.module.asset.entity.AssetType;
import net.datatp.module.asset.entity.DepreciableAsset;
import net.datatp.module.asset.entity.TaskableAsset;
import net.datatp.module.asset.entity.TaskableAsset.AssetTaskStatus;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;

@Service("AssetService") @Getter
public class AssetService {
  @Autowired
  private AssetLogic assetLogic;
  
  @Autowired
  private DepreciableAssetLogic depreciableAssetLogic;
  
  @Autowired
  private TaskableAssetLogic taskableAssetLogic;
  
  @Transactional(readOnly = true)
  public AssetType getAssetTypeById(ClientInfo client, ICompany company, Long id) {
    return assetLogic.getAssetTypeById(client, company, id);
  }
  
  @Transactional(readOnly = true)
  public AssetType getAssetTypeByType(ClientInfo client, ICompany company, String type) {
    return assetLogic.getAssetTypeByType(client, company, type);
  }
  
  @Transactional
  public AssetType saveAssetType(ClientInfo client, ICompany company, AssetType type) {
    return assetLogic.saveAssetType(client, company, type);
  }
  
  @Transactional
  public List<SqlMapRecord> searchAssetTypes(ClientInfo client, ICompany company, SqlQueryParams sqlParams) {
    return assetLogic.searchAssetTypes(client, company, sqlParams);
  }
  
  @Transactional
  public boolean changeAssetTypeStorageState(ClientInfo client, ICompany company, ChangeStorageStateRequest req) {
    return assetLogic.changeAssetTypeStorageState(client, company, req);
  }
  
  @Transactional(readOnly = true)
  public Asset getAssetById(ClientInfo client, ICompany company, Long id) {
    return assetLogic.getAssetById(client, company, id);
  }

  @Transactional
  public Asset saveAsset(ClientInfo client, ICompany company, Asset asset) {
    return assetLogic.saveAsset(client, company, asset);
  }
  
  @Transactional
  public Asset calculateAssetDepreciation(ClientInfo client, ICompany company, Asset asset) {
    return assetLogic.calculateAssetDepreciation(client, company, asset);
  }
  
  @Transactional
  public List<SqlMapRecord> searchAssets(ClientInfo client, ICompany company, SqlQueryParams sqlParams) {
    return assetLogic.searchAssets(client, company, sqlParams);
  }

  @Transactional
  public boolean changeAssetStorageState(ClientInfo client, ICompany company, ChangeStorageStateRequest req) {
    return assetLogic.changeAssetStorageState(client, company, req);
  }
  
  @Transactional(readOnly = true)
  public List<AssetResponsibility> findAssetResponsibilities(ClientInfo client, ICompany company, Long employeeId) {
    return assetLogic.findAssetResponsibilities(client, company, employeeId);
  }
  
  @Transactional(readOnly = true)
  public DepreciableAsset getDepreciableAssetById(ClientInfo client, ICompany company, Long id) {
    return depreciableAssetLogic.getDepreciableAssetById(client, company, id);
  }

  @Transactional(readOnly = true)
  public DepreciableAsset getDepreciableAssetByAssetId(ClientInfo client, ICompany company, Long assetId) {
    return depreciableAssetLogic.getDepreciableAssetByAssetId(client, company, assetId);
  }
  
  @Transactional
  public DepreciableAsset saveDepreciableAsset(ClientInfo client, ICompany company, DepreciableAsset depreciableAsset) {
    return depreciableAssetLogic.saveDepreciableAsset(client, company, depreciableAsset);
  }

  @Transactional(readOnly = true)
  public TaskableAsset getTaskableAsset(ClientInfo client, ICompany company, Long id) {
    return taskableAssetLogic.getTaskableAsset(client, company, id);
  }

  @Transactional
  public TaskableAsset saveTaskableAsset(ClientInfo client, ICompany company, TaskableAsset tAsset) {
    return taskableAssetLogic.saveTaskableAsset(client, company, tAsset);
  }

  @Transactional
  public TaskableAsset handleTaskableAsset(ClientInfo client, ICompany company, TaskableAsset tAsset) {
    return taskableAssetLogic.handleTaskableAsset(client, company, tAsset);
  }
  
  @Transactional
  public TaskableAsset updateAssetTaskStatus(ClientInfo client, ICompany company, Long tAssetId, AssetTaskStatus status) {
    return taskableAssetLogic.updateAssetTaskStatus(client, company, tAssetId, status);
  }

  @Transactional
  public TaskableAsset initTaskableAsset(ClientInfo client, ICompany company, TaskableAsset tAsset) {
    return taskableAssetLogic.initTaskableAsset(client, company, tAsset);
  }
  
  @Transactional
  public TaskableAsset computeSendToEmails(ClientInfo client, ICompany company, TaskableAsset taskableAsset) {
    return taskableAssetLogic.computeSendToEmails(client, company, taskableAsset);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchTaskableAssets(ClientInfo client, ICompany company, SqlQueryParams sqlParams) {
    return taskableAssetLogic.searchTaskableAssets(client, company, sqlParams);
  }
  
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchAssetTasks(ClientInfo client, ICompany company, SqlQueryParams sqlParams) {
    return taskableAssetLogic.searchAssetTasks(client, company, sqlParams);
  }
  
  @Transactional(readOnly = true)
  public List<TaskableAsset> findUsedAssets(ClientInfo client, ICompany company, FindAssetTimeConflictParams params) {
    return taskableAssetLogic.findUsedAssets(client, company, params);
  }
}
