package net.datatp.module.asset;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import net.datatp.util.text.DateUtil;

@Getter
public class FindAssetTimeConflictParams {
  private Long assetId;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date fromTime;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date toTime;
}
