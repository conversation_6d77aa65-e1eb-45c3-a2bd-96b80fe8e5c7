package cloud.datatp.fforwarder.price.entity;

import cloud.datatp.fforwarder.price.common.BaseTransportCharge;
import cloud.datatp.fforwarder.price.common.ChargeTarget;
import cloud.datatp.fforwarder.price.common.PriceFeedbackUpdateMessagePlugin;
import cloud.datatp.fforwarder.price.common.SeaFCLPriceGroup;
import cloud.datatp.fforwarder.price.common.SeaNotificationTemplate;
import cloud.datatp.fforwarder.price.entity.CommissionDistribution.CommissionDistributionMethod;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.settings.message.entity.MessageType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.entity.Account;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.module.settings.currency.entity.Currency;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;

@Entity
@Table(
  name = SeaFclTransportCharge.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = SeaFclTransportCharge.TABLE_NAME + "_code",
      columnNames = {"code"})
  },
  indexes = {
    @Index(columnList = "company_id"),
    @Index(columnList = "code"),
    @Index(columnList = "valid_from"),
    @Index(columnList = "valid_to"),
    @Index(columnList = "from_location_code"),
    @Index(columnList = "to_location_code"),
    @Index(columnList = "group_type"),
    @Index(columnList = "purpose"),
    @Index(columnList = "storage_state")
  }
)
@DeleteGraphs({
  @DeleteGraph(target = TransportAdditionalCharge.class, joinField = "sea_fcl_charge_id", joinType = DeleteGraphJoinType.OneToMany),
  @DeleteGraph(target = CommissionDistribution.class, joinField = "sea_fcl_charge_id", joinType = DeleteGraphJoinType.OneToMany),
  @DeleteGraph(target = TransportFrequency.class, joinField = "sea_fcl_charge_id", joinType = DeleteGraphJoinType.OneToMany),
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter @Slf4j
public class SeaFclTransportCharge extends BaseTransportCharge {
  @Serial
  private static final long serialVersionUID = 1L;

  public enum GroupType {
    NONE_US, US_ROUTE, SPECIAL;

    public static GroupType parse(String token) {
      if (token == null || token.trim().isEmpty()) return NONE_US;
      for (GroupType status : values()) {
        if (status.name().equalsIgnoreCase(token.trim())) return status;
      }
      return NONE_US;
    }
  }

  public static final String TABLE_NAME = "lgc_price_sea_fcl_charge";
  public static final String SEQUENCE = "lgc:lgc_price_sea_fcl_charge";

  @Setter
  @NotNull
  private String code;

  @Embedded
  private SeaFCLPriceGroup priceGroup = new SeaFCLPriceGroup();

  @Column(name = "group_type")
  @Enumerated(EnumType.STRING)
  private GroupType groupType = GroupType.NONE_US;

  @Column(name = "final_terminal_location_label")
  private String finalTerminalLocationLabel;

  @Column(name = "carrier_partner_id")
  protected Long carrierPartnerId;

  @Column(name = "carrier_label")
  protected String carrierLabel;

  @Column(name = "handling_agent_partner_id")
  protected Long handlingAgentPartnerId;

  @Column(name = "handling_agent_partner_label")
  protected String handlingAgentPartnerLabel;

  // Note from Line/ Carrier
  @Column(length = 1024 * 32)
  private String remarks;

  @Column(name = "container_handling_type")
  private String containerHandlingType;

  // for dem, det, storage
  @Column(name = "free_time")
  private String freeTime;

  @Column(name = "surcharge_note")
  private String surchargeNote;

  @Column(name = "cut_off_time")
  private String cutOffTime;

  @Column(name = "depart_time")
  private String departTime;

  @Column(name = "frequency")
  private String frequency;

  @Column(name = "transit_time")
  private String transitTime;

  @Column(name = "transit_port")
  private String transitPort;

  @Column(name = "commodity")
  private String commodity;

  @Transient
  private List<MapObject> feedbacks = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "sea_fcl_charge_id", referencedColumnName = "id")
  private List<TransportFrequency> transportFrequencies = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "sea_fcl_charge_id", referencedColumnName = "id")
  private List<TransportAdditionalCharge> additionalCharges = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "sea_fcl_charge_id", referencedColumnName = "id")
  private List<CommissionDistribution> commissionDistributions = new ArrayList<>();

  public SeaFclTransportCharge(MapObject record) {
    mapFrom(record);
  }

  @JsonIgnore
  @Access(AccessType.PROPERTY)
  @Basic(fetch = FetchType.EAGER)
  @Column(name = "feedbacks", length = 64 * 1024)
  public String getFeedbackJson() {
    if (this.feedbacks == null) return null;
    return DataSerializer.JSON.toString(this.feedbacks);
  }

  public void setFeedbackJson(String json) {
    if (StringUtil.isEmpty(json)) {
      this.feedbacks = null;
    } else {
      this.feedbacks = DataSerializer.JSON.fromString(json, new TypeReference<>() { });
    }
  }

  public SeaFclTransportCharge withCurrency(Currency currency) {
    this.currency = currency.getName();
    return this;
  }

  public SeaFclTransportCharge withCurrency(String name) {
    this.currency = name;
    return this;
  }

  public SeaFclTransportCharge withAdditionalCharge(TransportAdditionalCharge... additionalCharge) {
    additionalCharges = Arrays.addToList(additionalCharges, additionalCharge);
    return this;
  }

  public SeaFclTransportCharge withCommission(CommissionDistribution... commission) {
    commissionDistributions = Arrays.addToList(commissionDistributions, commission);
    return this;
  }

  public SeaFclTransportCharge withFrequency(TransportFrequency... fre) {
    transportFrequencies = Arrays.addToList(transportFrequencies, fre);
    return this;
  }

  public List<TransportFrequency> getTransportFrequencies() {
    if (Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    return transportFrequencies;
  }

  public void setTransportFrequencies(List<TransportFrequency> frequencies) {
    if (Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    this.transportFrequencies.clear();
    if (frequencies != null) this.transportFrequencies.addAll(frequencies);
  }

  public List<TransportAdditionalCharge> getAdditionalCharges() {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    return additionalCharges;
  }

  public void setAdditionalCharges(List<TransportAdditionalCharge> addCharges) {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    this.additionalCharges.clear();
    if (addCharges != null) this.additionalCharges.addAll(addCharges);
  }

  public List<CommissionDistribution> getCommissionDistributions() {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    return commissionDistributions;
  }

  public void setCommissionDistributions(List<CommissionDistribution> commissions) {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    this.commissionDistributions.clear();
    if (commissions != null) this.commissionDistributions.addAll(commissions);
  }

  public CRMMessageSystem toPricingFeedbackUpdateMessage(Account assigneeAccount, Account saleAccount, String feedback) {
    CRMMessageSystem message = new CRMMessageSystem();
    message.setContent(SeaNotificationTemplate.buildMailMessage(this, saleAccount, feedback));
    message.setScheduledAt(new Date());
    message.setMessageType(MessageType.MAIL);
    message.setReferenceId(id);
    message.setReferenceType(SeaFclTransportCharge.TABLE_NAME);
    message.setPluginName(PriceFeedbackUpdateMessagePlugin.PLUGIN_TYPE);
    message.setRecipients(new HashSet<>(Collections.singletonList(assigneeAccount.getEmail())));
    MapObject metadata = new MapObject();
    metadata.put("fromEmail", saleAccount.getEmail());
    metadata.put("subject", "PRICE FEEDBACK UPDATE - SEA FCL");
    metadata.put("to", java.util.Collections.singletonList(assigneeAccount.getEmail()));
    message.setMetadata(metadata);
    return message;
  }

  private TransportAdditionalCharge getTransportAdditionalCharge(String name, String[] split, double amount) {
    TransportAdditionalCharge addCharge = new TransportAdditionalCharge(name.toUpperCase());
    String unit = split[2];
    addCharge.setPurpose(purpose);
    if (Purpose.EXPORT.equals(purpose)) addCharge.setTarget(ChargeTarget.ORIGIN);
    if (Purpose.IMPORT.equals(purpose)) addCharge.setTarget(ChargeTarget.DESTINATION);
    addCharge.setUnitPrice(amount);
    addCharge.setQuantity(1);
    addCharge.setCurrency("USD");
    addCharge.setTotal(amount);
    addCharge.setUnit(unit);
    return addCharge;
  }

  public SeaFclTransportCharge computeFromMapObject(MapObject record) {
    if (record == null) return this;
    mapFrom(record);

    carrierPartnerId = record.getLong("carrierPartnerId", null);
    carrierLabel = record.getString("carrierLabel", null);

    handlingAgentPartnerId = record.getLong("handlingAgentPartnerId", null);
    handlingAgentPartnerLabel = record.getString("handlingAgentPartnerLabel", null);

    finalTerminalLocationLabel = record.getString("finalTerminalLocationLabel", null);
    containerHandlingType = record.getString("containerHandlingType", null);

    groupType = GroupType.valueOf(record.getString("groupType", "NONE_US"));
    surchargeNote = record.getString("surchargeNote", null);
    remarks = record.getString("remarks", "");
    freeTime = record.getString("freeTime", "");
    commodity = record.getString("commodity", "");
    priceGroup = Objects.ensureNotNull(priceGroup, SeaFCLPriceGroup::new).computeFrom(record);


    if (record.containsKey("feedbacks")) {
      try {
        Object feedbackData = record.get("feedbacks");
        if (feedbackData instanceof List) {
          this.feedbacks = (List<MapObject>) feedbackData;
        } else if (feedbackData instanceof String) {
          this.feedbacks = DataSerializer.JSON.fromString((String) feedbackData, new TypeReference<>() { });
        } else {
          this.feedbacks = new ArrayList<>();
        }
      } catch (Exception e) {
        log.error("Failed to set feedback", e);
        log.error(e.getMessage(), e);
        this.feedbacks = null;
      }
    }

    TransportFrequency transportFrequency = TransportFrequency.creator(record);
    this.departTime = transportFrequency.getDepartTime();
    this.cutOffTime = transportFrequency.getCutOffTime();
    this.frequency = transportFrequency.getLabel();
    this.transitTime = transportFrequency.getTransitTime();
    this.transitPort = transportFrequency.getTransit();
    setTransportFrequencies(Collections.singletonList(transportFrequency));

    String chargeKey = "addCharge:TLX:SHIPMENT";
    String chargeName = "B_TLX";

    if (record.containsKey(chargeKey)) {
      double amount = 0;
      String note = "";
      try {
        amount = record.getDouble(chargeKey, 0d);
      } catch (Exception e) {
        note = record.getString(chargeKey, "");
      }

      if (amount != 0 || !note.isEmpty() || !isNew()) {
        final String[] split = chargeKey.split(":");
        String name = split[1];

        boolean match = false;
        for (TransportAdditionalCharge next : getAdditionalCharges()) {
          if (name.equals(next.getLabel())) {
            next.setUnitPrice(amount);
            next.setNote(note);
            match = true;
            break;
          }
        }
        if (!match) {
          final TransportAdditionalCharge addCharge = getTransportAdditionalCharge(chargeName, split, amount);
          addCharge.setName(chargeName);
          addCharge.setNote(note);
          withAdditionalCharge(addCharge);
        }
      }
    }

    List<String> commissionKey = Arrays.asList("commission20DC", "commission40DC", "commission40HC", "commission45HC", "commission20RF", "commission40RF", "commission40NOR");
    for (String key : commissionKey) {
      double commissionPrice = record.getDouble(key, 0d);
      if (commissionPrice == 0) continue;
      CommissionDistribution commission = new CommissionDistribution(commissionPrice);
      commission.setMethod(CommissionDistributionMethod.QUANTITY);
      commission.setLabel(key.substring(10) + " KB");
      commission.setUnit(key.substring(10));
      withCommission(commission);
    }
    return this;
  }

  @Override
  public String getServiceType() {
    return "FCL";
  }

  @Override
  public String getSequence() {
    return SEQUENCE;
  }

  @Override
  public void set(ClientInfo client, Company company) {
    super.set(client, company);
    set(client, company, additionalCharges);
    set(client, company, commissionDistributions);
    set(client, company, transportFrequencies);
  }
}