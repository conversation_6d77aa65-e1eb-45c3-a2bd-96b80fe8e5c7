package cloud.datatp.fforwarder.price.common;

import cloud.datatp.fforwarder.settings.Purpose;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.MappedSuperclass;
import java.io.Serial;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.ShareableCompanyEntity;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.util.bean.BeanUtil;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@MappedSuperclass
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public abstract class BaseTransportCharge extends ShareableCompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  //@NotNull
  @Enumerated(EnumType.STRING)
  protected Purpose purpose = Purpose.EXPORT;

  protected String label = "N/A";

  @Column(name = "from_location_code")
  protected String fromLocationCode;

  @Column(name = "from_location_label")
  protected String fromLocationLabel;

  @Column(name = "to_location_code")
  protected String toLocationCode;

  @Column(name = "to_location_label")
  protected String toLocationLabel;

  protected String route;

  @Column(name = "carrier_route")
  protected String carrierRoute;

  @Column(name = "assignee_account_id")
  protected Long assigneeAccountId;

  @Column(name = "assignee_label")
  protected String assigneeLabel;

  @Enumerated(EnumType.STRING)
  @Column(name = "edit_mode")
  protected EditMode editMode = EditMode.VALIDATED;

  protected String currency;

  @Column(length = 1024 * 32)
  protected String note;

  @Setter(value = AccessLevel.NONE)
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "valid_from")
  protected Date validFrom;

  @Setter(value = AccessLevel.NONE)
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "valid_to")
  protected Date validTo;

  public String genCodePrefix() {
    StringBuilder builder = new StringBuilder(purpose.getAbb());
    builder.append(getServiceType());
    Objects.assertNotNull(validTo, "Valid To must be not null!");
    final String dateFormat = DateUtil.asCompactYearMonth(validTo);
    builder.append(dateFormat);
    builder.append(companyId);
    return builder.toString();
  }

  protected void mapFrom(MapObject record) {
    final String purposeStr = record.getString("purpose", "");
    if (!purposeStr.isEmpty()) purpose = Purpose.valueOf(purposeStr);
    if (Purpose.EXPORT.equals(this.purpose)) {
      shareable = ShareableScope.ORGANIZATION;
    } else {
      shareable = ShareableScope.DESCENDANTS;
    }
    assigneeAccountId = record.getLong("assigneeAccountId", null);
    assigneeLabel = record.getString("assigneeLabel", null);

    fromLocationLabel = record.getString("fromLocationLabel", null);
    fromLocationCode = record.getString("fromLocationCode", null);
    fromLocationLabel = record.getString("fromLocationLabel", null);
    toLocationCode = record.getString("toLocationCode", null);
    toLocationLabel = record.getString("toLocationLabel", null);
    currency = record.getString("currency", "USD");

    String fromDateStr = record.getString("validFrom", "");
    if (StringUtil.isNotEmpty(fromDateStr)) setValidFrom(DateUtil.parseCompactDate(fromDateStr));
    String toDateStr = record.getString("validTo", "");
    if (StringUtil.isNotEmpty(toDateStr)) setValidTo(DateUtil.parseCompactDate(toDateStr));

    note = record.getString("note", null);
  }

  public abstract String getServiceType();

  public abstract String getSequence();

  public void setValidTo(Date validTo) {
    if (validTo == null) validTo = new Date();
    LocalDate localDate = validTo.toInstant()
        .atZone(ZoneId.systemDefault())
        .toLocalDate();
    LocalDateTime endOfDay = localDate.atTime(LocalTime.MAX);
    this.validTo = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
  }

  public void setValidFrom(Date validFrom) {
    if (validFrom == null) validFrom = new Date();
    LocalDate localDate = validFrom.toInstant()
        .atZone(ZoneId.systemDefault())
        .toLocalDate();
    LocalDateTime endOfDay = localDate.atTime(LocalTime.MIN);
    this.validFrom = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
  }

  protected void copy(BaseTransportCharge other) {
    if (other == null) return;
    List<Field> fields = BeanUtil.getFields(BaseTransportCharge.class);
    BeanUtil.copyAllFields(this, other, fields);
  }

  public void setCurrency(String currency) {
    if (StringUtil.isEmpty(currency)) this.currency = "USD";
    else this.currency = currency;
  }

  public BaseTransportCharge withPrivateShareableScope() {
    setShareable(ShareableScope.PRIVATE);
    return this;
  }

  public boolean isSameCompany(Long id) {
    return companyId.equals(id);
  }
}