import * as FeatherIcon from 'react-feather';
export * from './BBRefAssetType'

export enum AssetConditions {
  NEW = 'New', GOOD = 'Good', FUNCTIONAL = 'Functional'
};

export enum AssetResponsibilityStatuses {
  ACTIVE = "Active", PAST = "Past"
}

export enum DepreciationMethods {
  PERIOD = "period", PERCENT = "percent", VALUE = "value"
}

export enum AssetDepreciationPeriodUnits {
  YEARLY = "Yearly", MONTHLY = "Monthly"
}


export const AssetTaskStatus = {
  Pending: {
    label: 'Pending',
    value: 'Pending',
    color: 'warning',
    icon: FeatherIcon.Loader
  },
  Approved: {
    label: 'Approved',
    value: 'Approved',
    color: 'success',
    icon: FeatherIcon.CheckCircle
  },
  Rejected: {
    label: 'Rejected',
    value: 'Rejected',
    color: 'danger',
    icon: FeatherIcon.XCircle
  }
} as const;

export class AssetTaskStatusUtils {
  static getStatusInfo(status: string = 'Rejected') {
    const statusInfo = AssetTaskStatus[status as keyof typeof AssetTaskStatus];
    return statusInfo || AssetTaskStatus.Rejected;
  }

  static getAssetTaskStatusList() {
    return Object.values(AssetTaskStatus);
  }

  static isApproved(status?: string): boolean {
    if (!status) return false;
    return status === AssetTaskStatus.Approved.value || status === AssetTaskStatus.Rejected.value;
  }
}


export const AssetTaskType = {
  Car: {
    label: 'Car Schedule',
    value: 'Car',
    color: 'info',
    icon: FeatherIcon.Truck
  },
  MeetingRoom: {
    label: 'Meeting/Training Schedule',
    value: 'MeetingRoom',
    color: 'success',
    icon: FeatherIcon.Users
  },
  Other: {
    label: 'Other',
    value: 'Other',
    color: 'secondary',
    icon: FeatherIcon.Activity
  }
} as const;

export class AssetTaskTypeUtils {
  static getTypeInfo(type: string = 'Other') {
    const typeInfo = AssetTaskType[type as keyof typeof AssetTaskType];
    return typeInfo || AssetTaskType.Other;
  }

  static getTaskTypeList() {
    return Object.values(AssetTaskType);
  }
}
