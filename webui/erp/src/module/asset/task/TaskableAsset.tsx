import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, input, entity, app, grid, sql, util } from "@datatp-ui/lib";

import { T } from '../Dependency';
import { BBRefAsset } from "../BBRefAsset";
import { BBRefAccount } from "../../account";
import { BBRefMultiEmail } from "module/communication/message";

export class UITaskableAssetListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      context: 'system',
      service: 'AssetService',
      // changeStorageStateMethod: 'changeAssetTypeStorageState',
      searchMethod: 'searchTaskableAssets',
    }

    this.searchParams = {
      "params": {},
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "rangeFilters": [
        ...sql.createCreatedTimeFilter(),
        ...sql.createModifiedTimeFilter()
      ],
      "orderBy": {
        fields: ["modifiedTime"],
        fieldLabels: ["Modified Time"],
        selectFields: [],
        sort: "DESC"
      },
      "maxReturn": 1000
    }
  }

  withTaskType = (taskType: string) => {
    this.addSearchParam('taskType', taskType);
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }
}

export class UITaskableAssetList extends entity.DbEntityList {

  createVGridConfig(): grid.VGridConfig {
    let { type } = this.props;
    let config: grid.VGridConfig = {
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', T('Label'), 300),
          { name: 'taskType', label: T('Type'), width: 100 },
          { name: 'assetLabel', label: T('Asset'), width: 150 },
          { name: 'status', label: T('Status'), width: 100 },
          { name: 'fromDate', label: T('From'), width: 155, format: util.text.formater.compactDateTime },
          { name: 'toDate', label: T('To'), width: 155, format: util.text.formater.compactDateTime },
          { name: 'createdByAccountFullName', label: T('Creator'), width: 150 },
          { name: 'picAccountFullName', label: T('PIC'), width: 150 },
          { name: 'approverAccountFullName', label: T('Approver'), width: 150 },
          { name: 'description', label: T('Description'), width: 200 },
          ...entity.DbEntityListConfigTool.FIELD_ENTITY
        ]
      },
      toolbar: {
        actions: [
          // ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, T("Del")),
          // ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES(
          //   [entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED], !writeCap)
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true)
      },

      footer: {
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T("Select"), type)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      },
    };
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let rec = dRecord.record;
    let { appContext, pageContext, readOnly } = this.props;
    appContext.createHttpBackendCall('AssetService', 'getTaskableAsset', { id: rec.id })
      .withSuccessData((data: any) => {
        let observer = new entity.ComplexBeanObserver(data);
        let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let onPostCommit = (entity: any, uiEditor?: any) => {
            pageCtx.back();
            this.reloadData();
          }
          return (
            <UITaskableAssetEditor appContext={appContext} pageContext={pageContext} observer={observer} readOnly={readOnly}
              onPostCommit={onPostCommit} />
          )
        }
        pageContext.createPopupPage('taskable-asset', rec.label, createPageContent);
      })
      .call();
  }
}

interface UITaskableAssetEditorProps extends entity.AppDbEntityEditorProps {
  space?: 'user' | 'company';
}

export class UITaskableAssetEditor extends entity.AppDbEntityEditor<UITaskableAssetEditorProps> {
  computeSendToEmails = () => {
    let { appContext, observer } = this.props;
    let task = observer.getMutableBean();
    appContext.createHttpBackendCall('AssetService', 'computeSendToEmails', { taskableAsset: task })
      .withSuccessData((data: any) => {
        observer.replaceBeanProperty('sendToEmails', data['sendToEmails'])
        this.nextViewId();
        this.forceUpdate();
      })
      .call();
  }

  validateAssetTimeConflict = () => {
    let { appContext, observer } = this.props;
    let task = observer.getMutableBean();

    if (!task.fromTime || !task.toTime) {
      return;
    }

    let fromTime = util.TimeUtil.parseCompactDateTimeFormat(task.fromTime);
    let toTime = util.TimeUtil.parseCompactDateTimeFormat(task.toTime);

    if (fromTime.getHours() == 0 || toTime.getHours() == 0) {
      return;
    }

    if (fromTime.getTime() > toTime.getTime()) {
      bs.dialogShow('error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {T('From Time must be before To Time')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }


    if (!task.assetId && task.taskType === 'MeetingRoom') {
      return;
    }

    appContext.createHttpBackendCall('AssetService', 'findUsedAssets', {
      params: {
        assetId: task.assetId,
        fromTime: task.fromTime,
        toTime: task.toTime
      }
    })
      .withSuccessData((data: any[]) => {
        let conflictTasks = data.filter(usedTask => {
          if (!observer.isNewBean() && usedTask.id === task.id) {
            return false;
          }
          return true;
        });

        if (conflictTasks.length > 0) {
          let conflictTask = conflictTasks[0];
          let fromTimeStr = util.TimeUtil.format(util.TimeUtil.parseCompactDateTimeFormat(conflictTask.fromTime), 'HH:mm');
          let toTimeStr = util.TimeUtil.format(util.TimeUtil.parseCompactDateTimeFormat(conflictTask.toTime), 'HH:mm');

          bs.dialogShow('Error',
            <div className="text-danger fw-bold text-center py-3 border-bottom">
              <FeatherIcon.AlertCircle className="mx-2" />
              {`${conflictTask.assetLabel} is already booked from ${fromTimeStr} to ${toTimeStr}`}
              <br />
              <small className="text-muted">{T('Task')}: {conflictTask.label}</small>
            </div>,
            { backdrop: 'static', size: 'md' }
          );
        }
      })
      .call();
  }

  updateAssetTaskStatus = (status: 'Approved' | 'Rejected') => {
    let { appContext, observer } = this.props;
    let task = observer.getMutableBean();
    appContext.createHttpBackendCall('AssetService', 'updateAssetTaskStatus', { tAssetId: task.id, status: status })
      .withSuccessData((data: any) => {
        this.onPostCommit(data);
      })
      .call();
  }

  onPreCommit = (observer: entity.IBeanObserver) => {
    let taskObserver = observer as entity.ComplexBeanObserver;
    let task = taskObserver.getMutableBean();
    if (!task['label']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please Enter Contents.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please Enter Contents.');
    }

    if (task['fromTime'] === task['toTime']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please select Time.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please select Time.');
    }

    if (task['taskType'] === 'MeetingRoom' && (!task['assetId'] || !task['assetLabel'])) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please select Meeting Room.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please select Meeting Room.');
    }
  }

  onPostUpdateSendToEmail = (newVal: any) => {
    let { observer } = this.props;
    let task = observer.getMutableBean();
    let sendToEmails: any[] = task.sendToEmails || [];
    sendToEmails.push(newVal.email);
    task.sendToEmails = sendToEmails;
    this.nextViewId();
    this.forceUpdate();
  }

  onPostRemoveEmail = (sendToEmailsBean: any[]) => {
    let { observer } = this.props;
    let task = observer.getMutableBean();
    let sendToEmails: any[] = [];
    for (let emailBean of sendToEmailsBean) {
      sendToEmails.push(emailBean.email);
    }
    task.sendToEmails = sendToEmails;
    this.nextViewId();
    this.forceUpdate();
  }

  render() {
    let { appContext, pageContext, observer } = this.props;
    let task = observer.getMutableBean();
    let writeCap = pageContext.hasUserWriteCapability();
    let moderatorCap = pageContext.hasUserModeratorCapability();

    if (!task.fromTime) {
      let fromTime = util.TimeUtil.parseCompactDateTimeFormat(task.usingDate);
      fromTime.setHours(0)
      fromTime.setMinutes(0);
      task.fromTime = util.TimeUtil.toCompactDateTimeFormat(fromTime);
    }
    if (!task.toTime) {
      let toTime = util.TimeUtil.parseCompactDateTimeFormat(task.usingDate);
      toTime.setHours(0)
      toTime.setMinutes(0);
      task.toTime = util.TimeUtil.toCompactDateTimeFormat(toTime);
    }

    let timeMask: any = {
      fromTimeMask: null,
      toTimeMask: null
    }

    if (task.fromTime) {
      let fromTime = util.TimeUtil.parseCompactDateTimeFormat(task.fromTime);
      timeMask.fromTimeMask = util.TimeUtil.format(fromTime, 'HH:mm');
    }
    if (task.toTime) {
      let toTime = util.TimeUtil.parseCompactDateTimeFormat(task.toTime);
      timeMask.toTimeMask = util.TimeUtil.format(toTime, 'HH:mm');
    }

    let sendToEmails: any[] = [];

    if (task.sendToEmails) {
      for (let sendToEmail of task.sendToEmails) {
        let email = {
          'email': sendToEmail
        }
        sendToEmails.push(email);
      }
    }

    let assetFieldLabel = 'Asset';
    if (task.taskType === 'Car') assetFieldLabel = 'Car';
    if (task.taskType === 'MeetingRoom') assetFieldLabel = 'Meeting Room';

    let assigneeFieldLabel = 'Request by';
    if (task.taskType === 'MeetingRoom') assigneeFieldLabel = 'Host';

    return (
      <div className='flex-vbox' key={this.viewId}>
        <div className="flex-vbox p-1">
          <input.BBSelectField field="taskType" label="Type" bean={task}
            options={['Car', 'MeetingRoom', 'Other']} disable={!writeCap || !observer.isNewBean()}
            onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
              if (newVal === oldVal) return;
              task.taskType = newVal;
              task.assetId = null;
              task.assetLabel = null;
              this.computeSendToEmails();
            }} />

          <bs.Row>
            <bs.Col span={6}>
              <input.BBDateTimeField bean={task} label={T('Using Date')} field={'usingDate'} timeFormat={false}
                disable />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBTimeInputMask bean={timeMask} label={T('From Time')} field={'fromTimeMask'}
                disabled={!writeCap || !observer.isNewBean()}
                onInputChange={(bean: any, field: string, oldVal: any, newVal: string) => {
                  if (newVal === oldVal) return;
                  let fromTime = util.TimeUtil.parseCompactDateTimeFormat(task.usingDate);
                  fromTime.setHours(Number(newVal.substring(0, 2)));
                  fromTime.setMinutes(Number(newVal.substring(3, 5)));
                  observer.replaceBeanProperty('fromTime', util.TimeUtil.toCompactDateTimeFormat(fromTime));
                  this.nextViewId();
                  this.forceUpdate();
                  setTimeout(() => this.validateAssetTimeConflict(), 100);
                }} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBTimeInputMask bean={timeMask} label={T('To Time')} field={'toTimeMask'}
                disabled={!writeCap || !observer.isNewBean()}
                onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                  if (newVal === oldVal) return;
                  let toTime = util.TimeUtil.parseCompactDateTimeFormat(task.usingDate);
                  toTime.setHours(Number(newVal.substring(0, 2)));
                  toTime.setMinutes(Number(newVal.substring(3, 5)));
                  observer.replaceBeanProperty('toTime', util.TimeUtil.toCompactDateTimeFormat(toTime));
                  this.nextViewId();
                  this.forceUpdate();
                  setTimeout(() => this.validateAssetTimeConflict(), 100);
                }} />
            </bs.Col>
          </bs.Row>

          <input.BBTextField bean={task} label='Contents' field={'label'} disable={!writeCap} placeholder="Enter Contents ..."
            style={{ height: '8em' }} required />

          {task.taskType === 'Car' &&
            <>
              <bs.FormLabel>Start/End Place</bs.FormLabel>
              <bs.Row>
                <bs.Col span={6}>
                  <input.BBStringField bean={task} label='' field={'startPlace'} disable={!writeCap || !observer.isNewBean()}
                    placeholder="Enter Start Place ..." />
                </bs.Col>
                <bs.Col span={6}>
                  <input.BBStringField bean={task} label='' field={'endPlace'} disable={!writeCap || !observer.isNewBean()}
                    placeholder="Enter End Place ..." />
                </bs.Col>
              </bs.Row>
            </>
          }

          {
            !(task.taskType === 'Car' && observer.isNewBean()) ?
              <BBRefAsset
                appContext={appContext} pageContext={pageContext} required
                disable={!moderatorCap && (!writeCap || !observer.isNewBean())}
                label={T(assetFieldLabel)} placeholder={`Select ${assetFieldLabel}`} minWidth={300}
                taskable={true} taskType={task.taskType}
                bean={task} beanIdField={'assetId'} beanLabelField={'assetLabel'}
                onPostUpdate={(_inputUI, _bean, _selectOpt, _userInput) => {
                  setTimeout(() => this.validateAssetTimeConflict(), 100);
                }} />
              : null
          }

          <BBRefAccount
            appContext={appContext} pageContext={pageContext}
            label={T(assigneeFieldLabel)} placeholder='Enter User Account' disable={!writeCap || !observer.isNewBean()}
            bean={task} accountIdField={'picAccountId'} accountLabelField={'picAccountFullName'} />
          {
            !observer.isNewBean() ?
              <input.BBSelectField field="status" label="Status" bean={task}
                options={['Pending', 'Approved', 'Rejected']} disable={!pageContext.hasUserAdminCapability()} />
              : null
          }
          <input.BBTextField bean={task} label='Notes' field={'description'} disable={!writeCap} placeholder="Notes ..."
            style={{ height: '15em' }} />

          <div className='bb-field'>
            <bs.FormLabel>Send To Emails</bs.FormLabel>
            <BBRefMultiEmail beanIdField='email' beanLabelField='email' disable={!writeCap || !observer.isNewBean() || task.taskType === 'Car'}
              appContext={appContext} pageContext={pageContext} placeholder="Enter to email..." className='w-100'
              placement="bottom-start" offset={[0, 5]} bean={sendToEmails} minWidth={400}
              onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => this.onPostUpdateSendToEmail(selectOpt)}
              onRemove={this.onPostRemoveEmail}
            />
          </div>
        </div>

        <bs.Toolbar className='border' hide={!writeCap}>
          {observer.isNewBean() ?
            <label className="d-flex align-items-center gap-2 mb-0 me-2 cursor-pointer hover-opacity">
              <input.BBCheckboxField className='text-primary' bean={task} field='sendEmail' value={true} />
              <span className="fw-medium">{T('Send Email')}</span>
            </label>
            : null
          }

          <bs.Button laf='success' outline className="px-2 py-1 mx-1" style={{ width: 120 }}
            hidden={!moderatorCap || observer.isNewBean() || task.status !== 'Pending'}
            onClick={() => this.updateAssetTaskStatus('Approved')}>
            <FeatherIcon.X size={12} /> Approve
          </bs.Button>

          <bs.Button laf='danger' outline className="px-2 py-1 mx-1" style={{ width: 120 }}
            hidden={!moderatorCap || observer.isNewBean() || task.status !== 'Pending'}
            onClick={() => this.updateAssetTaskStatus('Rejected')}>
            <FeatherIcon.X size={12} /> Reject
          </bs.Button>
          <entity.ButtonEntityCommit hide={!writeCap && !observer.isNewBean()}
            appContext={appContext} pageContext={pageContext} observer={observer}
            commit={{
              entityLabel: 'TaskableAsset', context: 'company', service: 'AssetService', commitMethod: 'handleTaskableAsset'
            }}
            onPreCommit={this.onPreCommit}
            onPostCommit={this.onPostCommit} />
        </bs.Toolbar>
      </div >
    );
  }
}