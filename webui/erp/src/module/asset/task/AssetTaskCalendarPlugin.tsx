import React from 'react';
import * as FeatherIcon from 'react-feather'
import { calendar, bs, app, util, sql, entity } from '@datatp-ui/lib';

import { DbEntityTaskCalendarPlugin } from '../../common/wfms/task/DbEntityTaskCalendarPlugin';
import { DbEntityTaskList, DbEntityUserTaskListPlugin } from '../../common/wfms';
import { WAvatars } from 'module/account';
import { AssetTaskStatusUtils, AssetTaskTypeUtils } from '../models';
import { UITaskableAssetEditor, UITaskableAssetList, UITaskableAssetListPlugin } from './TaskableAsset';
import { T } from '../Dependency';
const SESSION = app.host.DATATP_SESSION;

export class DbEntityTaskAssetListPlugin extends entity.DbEntityListPlugin {
  entityRefType: string | null = null;

  constructor() {
    super([]);
    this.backend = {
      service: 'AssetService',
      searchMethod: 'searchAssetTasks',
    }
    this.searchParams = {
      filters: [
        ...sql.createSearchFilter()
      ],
      optionFilters: [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      rangeFilters: [
        ...sql.createCreatedTimeFilter(),
        ...sql.createModifiedTimeFilter()
      ],
      orderBy: {
        fields: ["modifiedTime"],
        fieldLabels: ["Modified Time"],
        selectFields: [],
        sort: "DESC"
      },
      maxReturn: 5000,
    }

    this.entityRefType = 'asset_taskable_asset';
    this.addSearchParam('entityRefType', 'asset_taskable_asset');
  }

  withSpace(space: 'company' | 'user') {
    this.addSearchParam('space', space);
    return this;
  }

  withTaskType = (taskType: string | null) => {
    if (taskType) this.addSearchParam('taskType', taskType);
    else this.removeSearchParam('taskType');
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let userParams = { params: this.searchParams }
    this.createBackendSearch(uiList, userParams).call();
  }
}

export class AssetTaskCalendarPlugin extends DbEntityTaskCalendarPlugin {
  constructor() {
    super('asset_taskable_asset', 'Asset');
    this.showActions = true;
    this.showFilter = true;
  }

  onAddTask = (context: calendar.CalendarContext, date: Date) => {
    let uiRoot = context.uiRoot as DbEntityTaskList;
    let { appContext, pageContext, space } = uiRoot.props;

    let tAsset = {
      usingDate: util.TimeUtil.javaCompactDateTimeFormat(new Date(date)),
    }

    appContext.createHttpBackendCall('AssetService', 'initTaskableAsset', { tAsset: tAsset })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let observer = new entity.BeanObserver(data);
          const onPostCommit = (_entity: any, _uiEditor?: bs.BaseComponent) => {
            pageCtx.back();
            uiRoot.reloadData();
          }
          return (
            <UITaskableAssetEditor
              appContext={appCtx} pageContext={pageCtx} observer={observer} onPostCommit={onPostCommit} />
          )
        }
        pageContext.createPopupPage("new-task", T("New Task"), createAppPage);

      })
      .call();
  }

  onViewTask = (context: calendar.CalendarContext, task: any) => {
    let uiRoot = context.uiRoot as DbEntityTaskList;
    let { appContext, pageContext, space } = uiRoot.props;
    let { entityRefId } = task;

    appContext.createHttpBackendCall('AssetService', 'getTaskableAsset', { id: entityRefId })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let observer = new entity.BeanObserver(data);
          const onPostCommit = (_entity: any, _uiEditor?: bs.BaseComponent) => {
            pageCtx.back();
            uiRoot.reloadData();
          }
          return (
            <UITaskableAssetEditor
              appContext={appCtx} pageContext={pageCtx} observer={observer} onPostCommit={onPostCommit} />
          );
        }
        const taskTypeInfo = AssetTaskTypeUtils.getTypeInfo(data['taskType']);
        pageContext.createPopupPage("asset-task", `${T(`${data['taskType'] ? taskTypeInfo.label : 'Task'}`)}: ${task.label}`, createAppPage);
      })
      .call();
  }

  onViewAllTasks = (context: calendar.CalendarContext, allTasks: any[] = []) => {
    let uiRoot = context.uiRoot as DbEntityTaskList;
    let { pageContext } = uiRoot.props;
    let activeFilter = uiRoot.pluginActiveFilter ? uiRoot.pluginActiveFilter : null;
    let plugin = new UITaskableAssetListPlugin();
    if (activeFilter) plugin.withTaskType(activeFilter);
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITaskableAssetList appContext={appCtx} pageContext={pageCtx} plugin={plugin} />
      )
    }
    let popupId = `view-all-tasks-${util.IDTracker.next()}`;
    pageContext.createPopupPage(popupId, "Asset Tasks", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  renderTaskSummaryList(context: calendar.CalendarContext, tasks: any[]): React.JSX.Element {
    let uiRoot = context.uiRoot as DbEntityTaskList;
    let activeFilter = uiRoot.pluginActiveFilter ? uiRoot.pluginActiveFilter : null;
    let uiTasks: any[] = [];
    for (let task of tasks) {
      if (activeFilter) {
        let entityRefData = JSON.parse(task.entityRefData);
        if (entityRefData['taskType'] !== activeFilter) continue;
      }
      uiTasks.push(this.renderTaskSummary(context, task))
    }
    let html = (
      <bs.Card header={`${this.label}(${uiTasks.length})`}>
        <bs.GreedyScrollable style={{ minHeight: 330 }}>
          {uiTasks}
        </bs.GreedyScrollable>
        <bs.Button laf='link' tabIndex={0}
          className="border-0 mx-1 p-1 w-100 text-center fs-9 text-decoration-none text-600"
          onClick={(e) => { this.onViewAllTasks(context, []) }}>
          {'View All'}
        </bs.Button>
      </bs.Card>
    )
    return html;
  }

  formatDateTime(dateStr: string) {
    let date = 'N/A';
    let time = 'N/A';
    if (dateStr && dateStr.length > 1) {
      date = dateStr.substring(0, 10);
      time = dateStr.substring(11, 16);
    }
    return {
      date: date,
      time: time
    };
  }

  formatEmployeeName(name: string): string {
    if (!name) return 'N/A';
    let parts = name.trim().toLowerCase().split(' ');
    parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));

    if (parts.length >= 3) {
      let initCount = parts.length - 2; // Luôn giữ lại 2 chữ cuối
      let initials = parts.slice(0, initCount).map(word => word[0] + '.').join('');
      return `${initials} ${parts.slice(-2).join(' ')}`;
    }

    return parts.join(' ');
  }

  renderTaskSummary(context: calendar.CalendarContext, task: any): React.JSX.Element {
    let uiRoot = context.uiRoot as DbEntityTaskList;
    let { appContext, pageContext } = uiRoot.props;
    let entityRefData: any = {
      taskType: 'OTHER',
      status: 'IN_PROGRESS',
      referenceId: null,
      referenceType: null
    }
    if (task.entityRefData != null) {
      entityRefData = JSON.parse(task.entityRefData)
    }

    const taskTypeInfo = AssetTaskTypeUtils.getTypeInfo(entityRefData['taskType']);
    const Icon = taskTypeInfo.icon;
    const statusInfo = AssetTaskStatusUtils.getStatusInfo(entityRefData['status']);
    const StatusIcon = statusInfo.icon;

    let html = (
      <div className='flex-vbox'>
        <div className={`ps-2 pt-2 align-items-center text-${taskTypeInfo.color} fw-bold`} style={{ fontSize: '0.9rem' }}>
          <Icon size={16} className={`text-${taskTypeInfo.color} me-2`} />
          {task.label}
        </div>

        <div className="d-flex align-items-start p-2 border-bottom cursor-pointer" onClick={() => this.onViewTask(context, task)}>
          <div className="flex-grow-1">
            <div className="fw-medium mb-1" style={{ fontSize: '0.9rem' }}>
              {entityRefData.assetLabel}
            </div>

            <div className="d-flex align-items-center justify-content-between mb-1">
              <div className="d-flex align-items-center small text-primary">
                <FeatherIcon.Calendar size={12} className="me-1" />
                <span className="me-2 ">{this.formatDateTime(entityRefData.usingDate).date}</span>
              </div>

              <div className="d-flex align-items-center small my-1 text-success fw-bold">
                <FeatherIcon.Clock size={12} className="me-1" />
                <span>{this.formatDateTime(entityRefData.fromTime).time}</span>
                <span className='m-1'>-</span>
                <span>{this.formatDateTime(entityRefData.toTime).time}</span>
              </div>

              <span className={`d-flex align-items-center px-2 py-1 rounded-2 bg-${statusInfo.color}-subtle text-${statusInfo.color}`}
                style={{ fontSize: '0.75rem', fontWeight: 500 }}>
                <StatusIcon size={12} className="me-1" />
                <span>{statusInfo.label}</span>
              </span>

            </div>

            <div className="d-flex align-items-center small text-muted">
              <span style={{ width: 90 }}>Requested By:</span>
              <WAvatars className='me-1' appContext={appContext} pageContext={pageContext}
                avatarIds={[entityRefData.picAccountId]} avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
              <span>{this.formatEmployeeName(entityRefData.picAccountFullName)}</span>
            </div>

            <div className="d-flex align-items-center small text-muted">
              <span style={{ width: 90 }}>Created By:</span>
              <WAvatars className='me-1' appContext={appContext} pageContext={pageContext}
                avatarIds={[entityRefData.createdByAccountId]} avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
              <span>{this.formatEmployeeName(entityRefData.createdByAccountFullName)}</span>
            </div>
          </div>
        </div>
      </div>
    );
    return html;
  }

  renderWeekCellTask = (context: calendar.CalendarContext, task: any): any => {
    let uiRoot = context.uiRoot as DbEntityTaskList;
    let { appContext, pageContext } = uiRoot.props;
    let entityRefData: any = {
      taskType: 'OTHER',
      status: 'IN_PROGRESS',
      referenceId: null,
      referenceType: null
    }
    if (task.entityRefData != null) {
      entityRefData = JSON.parse(task.entityRefData)
    }

    const taskTypeInfo = AssetTaskTypeUtils.getTypeInfo(entityRefData['taskType']);
    const Icon = taskTypeInfo.icon;
    const statusInfo = AssetTaskStatusUtils.getStatusInfo(entityRefData['status']);
    const StatusIcon = statusInfo.icon;

    let html = (
      <div className='flex-vbox rounded-2 bg-white border p-1 mb-1' style={{ cursor: 'pointer', transition: 'all 0.2s ease-in-out' }}
        onClick={(e) => {
          e.stopPropagation();
          this.onViewTask(context, task);
        }}>
        <div className={`ps-2 py-1 align-items-center text-center fw-bold border-bottom`} style={{ fontSize: '0.9rem' }}>
          <span className='fw-bold text-secondary'>{task.label}</span>
        </div>

        <div className="d-flex align-items-start p-1 cursor-pointer">
          <div className="flex-grow-1">
            <div className={`fw-bold mb-1 text-center text-${taskTypeInfo.color}`} style={{ fontSize: '0.9rem' }}>
              <Icon size={16} className={`me-2`} /> {entityRefData.assetLabel}
            </div>

            <div className="d-flex align-items-center justify-content-center small text-danger fw-bold">
              <FeatherIcon.Clock size={12} className="me-1" />
              <span>{this.formatDateTime(entityRefData.fromTime).time}</span>
              <span className='m-1'>-</span>
              <span>{this.formatDateTime(entityRefData.toTime).time}</span>

              <span className={`ms-2 px-2 py-1 align-items-center bg-${statusInfo.color}-subtle text-${statusInfo.color}`}
                style={{ fontSize: '0.75rem', fontWeight: 500, borderRadius: '50%' }} title={statusInfo.label}>
                <StatusIcon size={12} />
              </span>
            </div>

            <div className="d-flex align-items-center justify-content-center small text-muted mt-1">
              <WAvatars className='me-1' appContext={appContext} pageContext={pageContext}
                avatarIds={[entityRefData.picAccountId]} avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
              <span>{this.formatEmployeeName(entityRefData.picAccountFullName)}</span>
            </div>
          </div>
        </div>
      </div>
    );
    return html;
  }

  renderFilter = (context: calendar.CalendarContext) => {
    let uiRoot = context.uiRoot as DbEntityTaskList;
    const { appContext, pageContext } = uiRoot.props;
    return (
      <div className='flex-vbox'>
        <UICalendarFilter context={context} appContext={appContext} pageContext={pageContext} />
      </div>
    )
  }
}

interface UICalendarFilterProps extends app.AppComponentProps {
  context: calendar.CalendarContext
}

export class UICalendarFilter extends app.AppComponent<UICalendarFilterProps> {
  popoverId: string = 'calendar-filter-' + util.IDTracker.next();
  isFiltering: boolean = false;

  onFilterRecords = () => {
    this.isFiltering = !this.isFiltering;
    alert("TODO: Dan - Implement this method!!!")
    // this.forceUpdate();
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as DbEntityTaskList;
    let { plugin } = uiRoot.props;
    let pluginImpl = plugin as DbEntityTaskAssetListPlugin;

    return (
      <div className="flex-vbox">

        <div className="fw-bold border-bottom my-1 d-flex flex-grow-0 align-items-center justify-content-between">
          <span>Asset</span>
        </div>

        <div className="d-flex flex-wrap gap-1 mb-1">
          {/* All Tasks Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '140px' }}>
            <input className="form-check-input me-2" type="checkbox"
              checked={!uiRoot.pluginActiveFilter}
              onChange={() => {
                uiRoot.pluginActiveFilter = null;
                pluginImpl.withTaskType(uiRoot.pluginActiveFilter);
                uiRoot.reloadData();
              }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: !uiRoot.pluginActiveFilter ? '#525b75' : '#fff',
                borderColor: '#525b75'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              All Tasks
            </label>
          </div>

          {/* Car Tasks Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '140px' }}>
            <input
              className="form-check-input me-2"
              type="checkbox"
              checked={uiRoot.pluginActiveFilter === 'Car'}
              onChange={() => {
                if (uiRoot.pluginActiveFilter === 'Car') uiRoot.pluginActiveFilter = null;
                else uiRoot.pluginActiveFilter = 'Car';
                pluginImpl.withTaskType(uiRoot.pluginActiveFilter);
                uiRoot.reloadData();
              }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: uiRoot.pluginActiveFilter === 'Car' ? '#3874ff' : '#fff',
                borderColor: '#3874ff'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              Car Schedule
            </label>
          </div>

          {/* Meeting/Training Tasks Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '140px' }}>
            <input className="form-check-input me-2" type="checkbox"
              checked={uiRoot.pluginActiveFilter === 'MeetingRoom'}
              onChange={() => {
                if (uiRoot.pluginActiveFilter === 'MeetingRoom') uiRoot.pluginActiveFilter = null;
                else uiRoot.pluginActiveFilter = 'MeetingRoom';
                pluginImpl.withTaskType(uiRoot.pluginActiveFilter);
                uiRoot.reloadData();
              }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: uiRoot.pluginActiveFilter === 'MeetingRoom' ? '#25b003' : '#fff',
                borderColor: '#25b003'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              Meeting/ Training
            </label>
          </div>

          {/* Other Tasks Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '140px' }}>
            <input className="form-check-input me-2" type="checkbox"
              checked={uiRoot.pluginActiveFilter === 'Other'}
              onChange={() => {
                if (uiRoot.pluginActiveFilter === 'Other') uiRoot.pluginActiveFilter = null;
                else uiRoot.pluginActiveFilter = 'Other';
                pluginImpl.withTaskType(uiRoot.pluginActiveFilter);
                uiRoot.reloadData();
              }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: uiRoot.pluginActiveFilter === 'Other' ? '#f4b400' : '#fff',
                borderColor: '#f4b400'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              Other
            </label>
          </div>
        </div>
      </div>
    );
  }
}
