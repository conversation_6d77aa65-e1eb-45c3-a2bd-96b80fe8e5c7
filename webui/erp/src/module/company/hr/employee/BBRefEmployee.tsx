import React from 'react';
import { entity, sql, grid } from "@datatp-ui/lib";
import { T } from "../Dependency";

interface BBRefEmployeeProps extends entity.BBRefEntityProps {
  beanIdField: string;
  beanLabelField: string;
  department?: string;
  selectedId?: 'employeeId' | 'accountId' | 'loginId';
  allCompany?: boolean;
}

export class BBRefEmployee extends entity.BBRefEntity<BBRefEmployeeProps> {
  protected createPlugin() {
    let { department, selectedId, beanIdField, beanLabelField, allCompany } = this.props

    let loadMethod = 'getEmployee';
    let id = 'id';
    if (selectedId === 'accountId') {
      loadMethod = 'getEmployeeByAccount';
      id = 'accountId';
    } else if (selectedId === 'loginId') {
      loadMethod = 'getEmployeeByLoginId';
      id = 'loginId';
    }

    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'company',
        service: 'HRService',
        searchMethod: 'searchEmployees',
        loadMethod: loadMethod,

        createSearchParams: (searchParams: sql.SqlSearchParams, _userInput: string) => {
          searchParams.maxReturn = 300
          searchParams.params = {
            'departmentName': department,
            'allCompany': !!allCompany
          }
          searchParams.optionFilters = [
            sql.createStorageStateFilter(
              [
                entity.StorageState.ACTIVE,
                entity.StorageState.ARCHIVED,
                entity.StorageState.JUNK,
                entity.StorageState.DEPRECATED
              ]),
          ]
          return searchParams
        },
      },

      bean: {
        idField: beanIdField,
        labelField: beanLabelField,
      },

      refEntity: {
        idField: id,
        labelField: 'label',
        labelFunc(record) {
          let bfsoneUsername: string = record['bfsoneUsername'];
          if (bfsoneUsername) return `${bfsoneUsername.toUpperCase()} - ${record['label']}`
          else return record['label']
        },
        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', T('Full Name'), 250),
            { name: 'loginId', label: T('Login Id'), width: 150 },
            { name: 'bfsoneCode', label: T('BFSOne ID'), width: 150 },
            { name: 'bfsoneUsername', label: T('BFSOne Username'), width: 200 },
            { name: 'mobile', label: T('Mobile'), width: 150 },
            { name: 'nickname', label: T('Nickname'), width: 150 },
            { name: 'identificationNo', label: T('Identities'), width: 150 },
            { name: 'priority', label: T('Priority'), width: 150, state: { visible: false } },
            {
              name: "employeeRole", label: T("Role"),
              customRender(_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) {
                let record = dRecord.record;
                let cssCustom = ''
                let val = 'N/A'
                if (record[_field.name]) val = record[_field.name]
                if (val === 'Manager') cssCustom = 'fw-bold'
                return (<div className={`text-wrap position-relative ${cssCustom}`}>{val}</div>);
              },
            },
            { name: "departmentName", label: T("Department Name"), state: { visible: false }, width: 180 },
            { name: "departmentLabel", label: T("Department"), width: 180 },
            { name: 'description', label: T('Description'), width: 300 },
            { name: 'companyId', label: T('Company Id'), width: 150, state: { visible: false } },
            ...entity.DbEntityListConfigTool.FIELD_ENTITY

          ]
        }
      }
    }

    return new entity.BBRefEntityPlugin(config);
  }
}