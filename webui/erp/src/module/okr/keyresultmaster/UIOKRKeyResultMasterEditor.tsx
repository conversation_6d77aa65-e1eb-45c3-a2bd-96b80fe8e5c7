import React from "react";
import * as FeatherIcon from 'react-feather';
import { app, bs, input, entity } from '@datatp-ui/lib';

import { T } from "../Dependency";
import { OKRPermissionHelper } from "../project/OKRPermissionHelper";
import { UIOKRAutomationConfig } from "./automation/OKRAutomation";
import { BBRefUnit } from "module/settings";
import { BBRefEmployee } from "module/company/hr";
import { UIOKRKeyResultMasterChangeRequest, UIOKRKeyResultMasterChangeRequestList, UIOKRKeyResultMasterChangeRequestListPlugin } from "./automation/okr/UIOKRKeyResultMasterChangeRequest";
import { EntityChangeRequestStatus } from "../models";

interface UIOKRKeyResultMasterQuarterTargetEditorProps extends entity.AppDbEntityProps {
  permissionHelper: OKRPermissionHelper;
  editableTarget?: boolean;
  editableTargetAdjust?: boolean;
}
export class UIOKRKeyResultMasterQuarterTargetEditor extends entity.AppDbEntity<UIOKRKeyResultMasterQuarterTargetEditorProps> {
  render() {
    let { observer, editableTarget, editableTargetAdjust, pageContext } = this.props;
    let quarterTarget = observer.getMutableBean();

    return (
      <div className="flex-vbox">
        <bs.Row>
          <bs.Col span={6}>
            <input.BBNumberField field="quarter1Target" label="Quarter 1 Original Target" bean={quarterTarget}
              maxPrecision={2} disable={!editableTarget} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBNumberField field="quarter1TargetAdjust" label="Quarter 1 Adjust Target" bean={quarterTarget}
              maxPrecision={2} disable={!pageContext.hasUserAdminCapability() && !editableTargetAdjust} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBNumberField field="quarter2Target" label="Quarter 2 Original Target" bean={quarterTarget}
              maxPrecision={2} disable={!editableTarget} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBNumberField field="quarter2TargetAdjust" label="Quarter 2 Adjust Target" bean={quarterTarget}
              maxPrecision={2} disable={!pageContext.hasUserAdminCapability() && !editableTargetAdjust} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBNumberField field="quarter3Target" label="Quarter 3 Original Target" bean={quarterTarget}
              maxPrecision={2} disable={!editableTarget} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBNumberField field="quarter3TargetAdjust" label="Quarter 3 Adjust Target" bean={quarterTarget}
              maxPrecision={2} disable={!pageContext.hasUserAdminCapability() && !editableTargetAdjust} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBNumberField field="quarter4Target" label="Quarter 4 Original Target" bean={quarterTarget}
              maxPrecision={2} disable={!editableTarget} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBNumberField field="quarter4TargetAdjust" label="Quarter 4 Adjust Target" bean={quarterTarget}
              maxPrecision={2} disable={!pageContext.hasUserAdminCapability() && !editableTargetAdjust} />
          </bs.Col>
        </bs.Row>
      </div>
    )
  }
}
interface UIOKRKeyResultMasterFormProps extends entity.AppComplexEntityEditorProps {
  groupNames: string[];
  permissionHelper: OKRPermissionHelper;
}
class UIOKRKeyResultMasterForm extends entity.AppDbComplexEntity<UIOKRKeyResultMasterFormProps> {

  onUpdateDataInputMethod = (bean: any, field: string, oldVal: any, newVal: any) => {
    let { observer } = this.props;
    if (newVal !== 'Select') {
      observer.replaceBeanProperty('collectTotalTargetValue', false);
    } else if (newVal !== 'Automation') {
      observer.replaceBeanProperty('automationPlugin', null);
    }
    this.forceUpdate();
  }

  render() {
    let { appContext, pageContext, observer, groupNames, permissionHelper } = this.props;
    let keyMaster = observer.getMutableBean();
    let writeCap = permissionHelper.hasModeratorPermission();
    let quarterTargetOb = observer.createObserver('quarterTarget', {});

    if (!groupNames) groupNames = [];
    else {
      groupNames.push('');
      groupNames.sort();
    }
    let capability = ['None', 'Read', 'Write', "Moderator", 'Admin'];
    let capabilityLabel = [T('None'), T('Read'), T('Write'), T('Moderator'), T('Admin')];
    let dataInputMethodOptions = ['Manual', 'Automation'];

    return (
      <div className="flex-vbox border rounded my-1 p-2" style={{ minHeight: 500 }}>
        <input.BBStringField label="Label" bean={keyMaster} field="label" disable={!writeCap} />
        {pageContext.hasUserAdminCapability() ?
          <>
            <input.BBNumberField field="targetValue" label="Original Target (Deprecated)" bean={keyMaster}
              maxPrecision={2} disable />
            <input.BBNumberField field="targetAdjustValue" label="Adjust Target (Deprecated)" bean={keyMaster}
              maxPrecision={2} disable />
          </>
          : null
        }
        {
          keyMaster.allocatedBy ?
            <bs.FormLabel>
              Allocated By: <div className="text-info">{keyMaster.allocatedBy}</div>
            </bs.FormLabel>
            : null
        }
        <UIOKRKeyResultMasterQuarterTargetEditor
          appContext={appContext} pageContext={pageContext}
          observer={quarterTargetOb} permissionHelper={permissionHelper}
          editableTarget={writeCap} editableTargetAdjust={false} />
        <BBRefUnit
          appContext={appContext} pageContext={pageContext} required
          bean={keyMaster} beanIdField='unit' placeholder='Unit' label="Unit"
          disable={!writeCap || !observer.isNewBean()} />
        <bs.Row>
          <bs.Col span={6}>
            <BBRefEmployee
              appContext={appContext} pageContext={pageContext} bean={keyMaster} disable={!writeCap}
              beanIdField='mainContributorAccountId' beanLabelField='mainContributorFullName'
              label='PIC' placeholder='PIC' selectedId={'accountId'} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBSelectField bean={keyMaster} field={'important'} label="Important Level"
              optionLabels={['Less Important', 'Important']} options={['LessImportant', 'Important']} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBSelectField field="confidenceLevel" label="Confidence Level" bean={keyMaster}
              options={['High', 'Medium', 'Low']} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBSelectField
              label={T('Min Access Capability')} bean={keyMaster} field={"minAccessCapability"}
              options={capability} optionLabels={capabilityLabel} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
        {
          groupNames ?
            <input.BBSelectField
              field="groupName" label="Group" bean={keyMaster} options={groupNames} disable={!writeCap} />
            : <></>
        }
        {
          keyMaster.dataInputMethod === 'Automation' ?
            <bs.Row>
              <bs.Col span={6}>
                <input.BBSelectField
                  bean={keyMaster} label={T('Data Input Method')} field={'dataInputMethod'} options={dataInputMethodOptions}
                  disable={!writeCap} onInputChange={this.onUpdateDataInputMethod} />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBSelectField
                  bean={keyMaster} label={T('Data Source')} field={'automationDataSource'} options={['okr', 'bfsone']}
                  disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            :
            <input.BBSelectField
              bean={keyMaster} label={T('Data Input Method')} field={'dataInputMethod'} options={dataInputMethodOptions}
              disable={!writeCap} onInputChange={this.onUpdateDataInputMethod} />
        }
        {
          keyMaster.automationDataSource === 'okr' ?
            <input.BBCheckboxField className="form-label"
              bean={keyMaster} label={T('Target Dependent on Allocations')} field="collectTotalTargetValue"
              value={false} disable={!writeCap} />
            :
            <></>
        }
        <input.BBTextField label="Description" bean={keyMaster} field="description" disable={!writeCap}
          style={{ height: '5em' }} />
      </div>
    );
  }
}

interface UIOKRKeyResultMasterEditorProps extends entity.AppComplexEntityEditorProps {
  groupNames: string[];
  permissionHelper: OKRPermissionHelper;
}
export class UIOKRKeyResultMasterEditor extends entity.AppDbComplexEntityEditor<UIOKRKeyResultMasterEditorProps> {
  onReloadData = () => {
    let { appContext, observer } = this.props;
    let keyResultMaster = observer.getMutableBean();
    let dataCallback = (data: any) => {
      observer.replaceWith(data);
      this.nextViewId();
      this.forceUpdate();
    }
    appContext
      .createHttpBackendCall('OKRService', 'getKeyResultMaster', { id: keyResultMaster.id })
      .withSuccessData(dataCallback)
      .call();
  }

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    if (onPostCommit) onPostCommit(entity);
    this.onReloadData();
  }

  onRequestChange = () => {
    let { appContext, pageContext, observer, permissionHelper } = this.props;
    let keyMaster = observer.getMutableBean();

    let request = {
      targetResourceId: keyMaster.id,
      status: EntityChangeRequestStatus.Draft,
      changeRequest: {
        label: keyMaster.label,
        targetValue: keyMaster.targetValue,
        targetAdjustValue: keyMaster.targetAdjustValue,
        currentValue: keyMaster.currentValue,
        quarterTarget: keyMaster.quarterTarget,
        unit: keyMaster.unit,
        mainContributorFullName: keyMaster.mainContributorFullName,
        confidenceLevel: keyMaster.confidenceLevel,
        minAccessCapability: keyMaster.minAccessCapability,
        groupName: keyMaster.groupName,
        automationDataSource: keyMaster.automationDataSource,
        collectTotalTargetValue: keyMaster.collectTotalTargetValue,
        description: keyMaster.description
      }
    }

    appContext
      .createHttpBackendCall('OKRService', 'createChangeRequest', { request: request })
      .withSuccessData((data: any) => {
        let changeRequest = data;
        if (!changeRequest.requestShortDescription) changeRequest.requestShortDescription = 'Request Change Target'
        if (changeRequest.assigneeUserAccountId && changeRequest.targetCompanyId && changeRequest.targetResourceId) {
          changeRequest.status = EntityChangeRequestStatus.Submitted;
        }
        let createPopupContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIOKRKeyResultMasterChangeRequest appContext={appCtx} pageContext={pageCtx} keyResultMasterRoot={keyMaster}
              observer={new entity.ComplexBeanObserver(data)} onPostCommit={this.onPostCommit} permissionHelper={permissionHelper} />
          );
        }
        pageContext.createPopupPage('request-change-km', T(`Request Change Key Result Master`), createPopupContent, { size: 'lg', backdrop: 'static' })
      })
      .call();
  }

  onShowChangeRequest = () => {
    let { pageContext, observer, permissionHelper } = this.props;
    let keyMaster = observer.getMutableBean();
    let createPopupContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className="flex-vbox">
          <UIOKRKeyResultMasterChangeRequestList appContext={appCtx} pageContext={pageCtx} keyResultMasterRoot={keyMaster}
            plugin={new UIOKRKeyResultMasterChangeRequestListPlugin().withKeyResultMasterId(keyMaster.id)} permissionHelper={permissionHelper}
            onModifyBean={(bean, action?) => {
              this.onPostCommit(null);
            }} />
        </div>
      );
    }
    pageContext.createPopupPage('request-change-km', T(`Request Change Key Result Master`), createPopupContent, { size: 'lg', backdrop: 'static' })
  }

  render() {
    let { appContext, pageContext, observer, groupNames, permissionHelper } = this.props;
    let keyMaster = observer.getMutableBean();
    let writeCap = permissionHelper.hasModeratorPermission();

    if (keyMaster) {
      let isPIC = keyMaster.mainContributorAccountId === permissionHelper.accessAccountId;
      writeCap = (permissionHelper.hasWritePermission() && isPIC) || permissionHelper.hasModeratorPermission()
    }

    return (
      <div className="flex-vbox">
        <bs.VSplit>
          <bs.VSplitPane width={370}>
            <h5>{T('Info')}</h5>
            <bs.GreedyScrollable>
              <UIOKRKeyResultMasterForm
                appContext={appContext} pageContext={pageContext} observer={observer}
                groupNames={groupNames} permissionHelper={permissionHelper} />
            </bs.GreedyScrollable>
            <bs.Toolbar hide={!writeCap} className='border'>
              <bs.Button laf={`${keyMaster.changeRequest ? 'warning' : 'primary'}`} onClick={this.onShowChangeRequest}>
                <FeatherIcon.List size={12} /> Change Requests
              </bs.Button>
              <bs.Button laf='primary' disabled={false} onClick={this.onRequestChange}>
                <FeatherIcon.Edit size={12} /> Request Change
              </bs.Button>
              <entity.ButtonEntityCommit
                appContext={appContext} pageContext={pageContext}
                observer={observer}
                commit={
                  {
                    context: 'company', service: 'OKRService', commitMethod: 'saveKeyResultMaster',
                    entityLabel: T(`Key Master {{label}}`, { label: observer.getMutableBean().label })
                  }
                }
                onPostCommit={this.onPostCommit} />
            </bs.Toolbar>
          </bs.VSplitPane>
          <bs.VSplitPane>
            {
              keyMaster.dataInputMethod === 'Automation' ?
                <div className="flex-vbox">
                  <div className="flex-vbox">
                    <UIOKRAutomationConfig key={this.componentId} readOnly={!writeCap}
                      appContext={appContext} pageContext={pageContext} uiRoot={this} keyResultMaster={keyMaster}
                      dataSource={keyMaster.automationDataSource} />
                  </div>
                </div>
                :
                null
            }
          </bs.VSplitPane>
        </bs.VSplit>
      </div >
    );
  }
}

interface UINewOKRKeyResultMasterProps extends entity.AppComplexEntityEditorProps {
  permissionHelper: OKRPermissionHelper;
  groupNames: string[];
}
export class UINewOKRKeyResultMaster extends entity.AppDbComplexEntityEditor<UINewOKRKeyResultMasterProps> {
  render() {
    let { appContext, pageContext, observer, groupNames, permissionHelper } = this.props;
    let writeCap = permissionHelper.hasModeratorPermission();

    return (
      <div className="flex-vbox">
        <bs.GreedyScrollable>
          <UIOKRKeyResultMasterForm
            appContext={appContext} pageContext={pageContext} observer={observer}
            groupNames={groupNames} permissionHelper={permissionHelper} />
        </bs.GreedyScrollable>
        <bs.Toolbar hide={!writeCap} className='border'>
          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext}
            observer={observer}
            commit={
              {
                context: 'company', service: 'OKRService', commitMethod: 'saveKeyResultMaster',
                entityLabel: T(`Key Master {{label}}`, { label: observer.getMutableBean().label })
              }
            }
            onPostCommit={this.onPostCommit} />
        </bs.Toolbar>
      </div>
    );
  }
}
