import React from 'react';
import * as FeatherIcon from 'react-feather';
import { calendar as cal, bs, app, util } from '@datatp-ui/lib';

import { DbEntityTaskCalendarPlugin, DbEntityTaskCalendarPluginManger } from './DbEntityTaskCalendarPlugin';
import { DbEntityTaskList, DbEntityUserTaskListPlugin } from './DbEntityTaskList';
import { log } from 'console';

export class CustomCalendarPlugin extends cal.UICalendarPlugin {
  renderBody() {
    let { context, config } = this.props;
    let uiRoot = context.uiRoot as DbEntityTaskList;
    let { appContext, pageContext } = uiRoot.props;
    let date = config.selectedDate;

    let recordMap: Record<string, Array<any>> = {};
    let dayRecordMap: cal.DayRecordMap | undefined = context.getDateRecordMap().getByDay(date);
    let records = [];
    if (dayRecordMap) {
      records = dayRecordMap.getAllRecords();
    }

    for (let record of records) {
      let recs: Array<any> = recordMap[record.entityRefType];
      if (recs) {
        recs.push(record);
      } else {
        recordMap[record.entityRefType] = [record];
      }
    }
    let uiTaskGroups: Array<any> = [];
    for (let name in recordMap) {
      let taskPlugin = DbEntityTaskCalendarPluginManger.getPlugin(name);
      let tasks: any[] = recordMap[name];
      uiTaskGroups.push(taskPlugin.renderTaskSummaryList(context, tasks))
    }

    return (
      <div className="flex-vbox">
        <div className='flex-vbox flex-grow-0'>
          <UICalendarFilter context={context} appContext={appContext} pageContext={pageContext} />
        </div>
        {
          records && records.length > 0 ?
            <bs.ScrollableCards className='flex-vbox flex-grow-1'>
              {uiTaskGroups}
            </bs.ScrollableCards >
            :
            <div>There is no task on {config.selectedDate.toDateString()}</div>
        }
      </div>
    )
  }
}

export class UserCustomCalendarPlugin extends CustomCalendarPlugin {
}

export class CompanyCustomCalendarPlugin extends CustomCalendarPlugin {
}

interface UICalendarFilterProps extends app.AppComponentProps {
  context: cal.CalendarContext
}

export class UICalendarFilter extends app.AppComponent<UICalendarFilterProps> {
  popoverId: string = 'calendar-filter-' + util.IDTracker.next();

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as DbEntityTaskList;
    let { plugin, displayMode } = uiRoot.props;
    let uiRootPlugin = plugin as DbEntityUserTaskListPlugin;
    let uiTaskFilterMap: Map<string, any> = new Map<string, any>();
    let uiTaskFilters: Array<any> = [];
    let taskPlugins: Array<DbEntityTaskCalendarPlugin> = Object.values(DbEntityTaskCalendarPluginManger.pluginMap);

    if (!displayMode) displayMode = 'default';
    if (displayMode == 'custom' && uiRootPlugin.entityRefType) {
      uiRoot.isFiltering = true;
      uiRoot.activeFilter = uiRootPlugin.entityRefType;
    }

    for (let taskPlugin of taskPlugins) {
      if (taskPlugin.type === 'default') continue;
      uiTaskFilters.push(
        <div className="form-check d-flex align-items-center me-2" style={{ minWidth: '110px' }}>
          <input className="form-check-input me-2" type="checkbox"
            checked={uiRoot.activeFilter === taskPlugin.type || uiRootPlugin.entityRefType === taskPlugin.type}
            onChange={() => {
              uiRoot.isFiltering = true;
              uiRoot.activeFilter = taskPlugin.type;
              uiRoot.pluginActiveFilter = null;
              uiRootPlugin = uiRootPlugin.withEntityRefType(taskPlugin.type);
              uiRoot.reloadData();
            }}
            style={{
              width: '16px',
              height: '16px',
              cursor: 'pointer',
              backgroundColor: (uiRoot.activeFilter === taskPlugin.type || uiRootPlugin.entityRefType === taskPlugin.type) ? '#0f9d58' : '#fff',
              borderColor: '#0f9d58'
            }}
          />
          <label className="form-check-label" style={{ cursor: 'pointer' }}>
            {taskPlugin.label}
          </label>
        </div>
      );
      if (taskPlugin.showFilter) uiTaskFilterMap.set(taskPlugin.type, taskPlugin.renderFilter(context));
    }


    let pluginFilter = uiRoot.activeFilter ? uiTaskFilterMap.get(uiRoot.activeFilter) : null;
    if (uiRootPlugin.entityRefType) {
      pluginFilter = uiTaskFilterMap.get(uiRootPlugin.entityRefType);
    }
    let popoverLaf: 'warning' | 'secondary' =
      (displayMode != 'custom' && uiRoot.activeFilter) || uiRoot.pluginActiveFilter ? 'warning' : 'secondary';

    return (
      <bs.Popover key={`calendar-filter`} placement="bottom" className='flex-grow-0' closeOnTrigger='*'>
        <bs.PopoverToggle laf={popoverLaf}
          className='mx-1 p-1 flex-hbox' outline>
          <FeatherIcon.Filter size={12} /> Filter
        </bs.PopoverToggle>
        <bs.PopoverContent style={{ width: 200 }} >
          <div className="flex-vbox">
            <div className="flex-vbox flex-grow-0 px-1">
              {/* Only render filters if not in custom display mode */}
              {displayMode !== 'custom' && (
                <>
                  {/* All Tasks Checkbox */}
                  <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
                    <input className="form-check-input me-2" type="checkbox"
                      checked={!uiRoot.activeFilter && !uiRootPlugin.entityRefType}
                      onChange={() => {
                        uiRoot.isFiltering = false;
                        uiRoot.activeFilter = null;
                        uiRoot.pluginActiveFilter = null;
                        uiRootPlugin = uiRootPlugin.withEntityRefType(null);
                        uiRoot.reloadData();
                      }}
                      style={{
                        width: '16px',
                        height: '16px',
                        cursor: 'pointer',
                        backgroundColor: (!uiRoot.activeFilter && !uiRootPlugin.entityRefType) ? '#4285f4' : '#fff',
                        borderColor: '#4285f4'
                      }}
                    />
                    <label className="form-check-label" style={{ cursor: 'pointer' }}>
                      All Tasks
                    </label>
                  </div>
                  <div className="flex-vbox border-bottom gap-1 mb-1 ">
                    {uiTaskFilters}
                  </div>
                </>
              )}
              {
                displayMode == 'custom' && pluginFilter ?
                  <div className="d-flex flex-wrap border-bottom gap-1 mb-1 ">
                    {pluginFilter}
                  </div>
                  : null
              }
            </div>
          </div>
        </bs.PopoverContent>
      </bs.Popover>

    );
  }
}
