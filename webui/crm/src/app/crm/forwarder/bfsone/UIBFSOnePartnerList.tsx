import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, grid, sql, app, entity, input, util } from '@datatp-ui/lib';

import { UIBFSOneCustomer, UINewBFSOnePartnerEditor, UISyncBFSOnePartnerEditor } from './UIBFSOnePartner';
import { T } from '../price';
import { PartnerType } from './BBRefBFSOneCustomer';
import { buildTooltipValues, WRateFinderGridFilter } from '../price';

export type Space = 'User' | 'Company' | 'System'

export class UIBFSOneCustomerListPlugin extends entity.DbEntityListPlugin {
  space: Space;

  constructor(type: PartnerType = 'Customer', space: Space = 'User') {
    super([]);
    this.space = space;
    let group: string = 'CUSTOMERS';
    if (type === 'Agent') group = 'AGENTS'
    else if (type === 'Coloader') group = 'COLOADERS';

    this.backend = {
      context: 'company',
      service: 'BFSOnePartnerService',
      searchMethod: 'searchBFSOnePartners',
      deleteMethod: '',
      changeStorageStateMethod: '',
      entityLabel: 'Customer'
    }

    this.searchParams = {
      "params": {
        group: group,
        space: space,
      },
      "filters": [...sql.createSearchFilter()],
      "maxReturn": 5000
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { param: this.searchParams }).call();
  }

}

export class UIBFSOnePartnerCustomerListPlugin extends entity.DbEntityListPlugin {
  space: Space;

  constructor(type: PartnerType = 'Customer', space: Space = 'User') {
    super([]);
    this.space = space;
    let group: string = 'CUSTOMERS';
    if (type === 'Agent') group = 'AGENTS'
    else if (type === 'Coloader') group = 'COLOADERS';

    this.backend = {
      context: 'company',
      service: 'BFSOnePartnerService',
      searchMethod: 'searchBFSOnePartnerCustomers',
      deleteMethod: '',
      changeStorageStateMethod: '',
      entityLabel: 'Customer'
    }

    this.searchParams = {
      "params": {
        group: group,
        space: space,
      },
      "filters": [...sql.createSearchFilter()],
      "maxReturn": 200
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { param: this.searchParams }).call();
  }

}


export interface UIBFSOnePartnerListProps extends entity.DbEntityListProps {
  partnerType: PartnerType
}
export class UIBFSOnePartnerList extends entity.DbEntityList<UIBFSOnePartnerListProps> {

  createVGridConfig() {
    let { type, pageContext, partnerType, plugin } = this.props;

    let pluginImp: UIBFSOneCustomerListPlugin = plugin as UIBFSOneCustomerListPlugin;
    const space: Space = pluginImp.space;

    const renderTooltipAdvanced = (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
      const record = dRecord.record;
      const val = record[field.name] || 'N/A';

      // Build tooltip content from record fields
      const tooltipFields = [
        { key: 'investmentOrigin', label: 'Investment Origin' },
        { key: 'kcnLabel', label: 'KCN' },
        { key: 'address', label: 'Address' },
        { key: 'note', label: 'Note' },
      ];

      // Build tooltip content from record fields
      const { htmlFormat, textFormat } = buildTooltipValues(record, tooltipFields);

      const handleClick = () => {
        navigator.clipboard.writeText(textFormat);
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      };

      let offsetX = field.width || 120

      return (
        <bs.CssTooltip width={400} offset={{ x: offsetX, y: 0 }}>
          <bs.CssTooltipToggle >
            <div className='flex-hbox' onClick={handleClick}>
              {field.fieldDataGetter ? field.fieldDataGetter(record) : field.format ? field.format(val) : val}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent>
            {htmlFormat}
          </bs.CssTooltipContent>
        </bs.CssTooltip>
      );
    }

    const allowAccess: boolean = space === 'System' || space === 'User' || pageContext.hasUserModeratorCapability();

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          // entity.DbEntityListConfigTool.FIELD_ON_SELECT('bfsonePartnerCode', T('Code'), 120, 'fixed-left'),
          {
            name: 'bfsonePartnerCode', label: T('Code.'), width: 100, filterable: true,
            container: 'fixed-left',
            onClick: function (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) {
              let uiRoot = ctx.uiRoot as UIBFSOnePartnerList
              if (!allowAccess) {
                bs.dialogShow('Message',
                  <div className="text-danger text-center p-2">
                    You don't have permission to view information. Please contact your administrator.
                  </div>
                );
                return;
              } else {
                uiRoot.onSelect(dRecord);
              }
            },
          },

          {
            name: 'name', label: 'Name', width: 300, cssClass: 'px-1', filterable: true,
            customRender: renderTooltipAdvanced
          },
          { name: 'label', label: 'Label', width: 250, cssClass: 'px-1', state: { visible: false } },
          { name: 'personalContact', label: 'Personal Contact', state: { visible: false }, width: 200 },
          {
            name: 'taxCode', label: 'Tax Code', width: 150, filterable: true,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'email', label: 'Email', width: 180, state: { visible: allowAccess },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'cell', label: 'Cell', width: 150, state: { visible: allowAccess },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'routing', label: 'Routing', width: 150, state: { visible: allowAccess },
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'source', label: 'Source', width: 120, state: { visible: allowAccess },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'kcnLabel', label: 'KCN', width: 180, filterable: true,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'provinceLabel', label: 'Province', width: 180, state: { visible: allowAccess }, filterable: true,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'countryLabel', label: 'Country', width: 180, state: { visible: allowAccess }, filterable: true,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'investmentOrigin', label: 'Investment Origin', width: 300, state: { visible: allowAccess }, filterable: true,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'address', label: 'Address', width: 300, state: { visible: allowAccess },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'note', label: 'Note', width: 350, state: { visible: allowAccess },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'modifiedTime', label: T('Modified Time'), width: 170,
            filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'createdTime', label: T('Created Time'), width: 170,
            filterable: true, filterableType: 'date', format: util.text.formater.compactDateTime,
            customRender: renderTooltipAdvanced
          },
        ]
      },
      toolbar: {
        hide: true,
      },
      footer: {
        default: {
          render: (ctx: grid.VGridContext) => {
            let uiList = ctx.uiRoot as UIBFSOnePartnerList;
            let { appContext, pageContext } = uiList.props;
            return (
              <bs.Toolbar className='border'>

                <bs.Button laf='primary' onClick={this.syncFromBFSOne} className='me-1'
                  hidden={!pageContext.hasUserModeratorCapability()}>
                  <FeatherIcon.Globe size={12} className='me-2' />
                  Fetch Partner
                </bs.Button>

                <entity.WButtonEntityNew hide={!pageContext.hasUserWriteCapability()} className='mx-2'
                  appContext={appContext} pageContext={pageContext}
                  label={partnerType} onClick={this.onNew} />

              </bs.Toolbar>
            );
          }
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(`${partnerType} Selector`, type)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }

  syncFromBFSOne = () => {
    let { pageContext, partnerType } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let observer = new entity.ComplexBeanObserver({});
      return (<UISyncBFSOnePartnerEditor appContext={appCtx} pageContext={pageCtx} observer={observer} partnerType={partnerType} />);
    };
    let popupLabel: string = `Sync BFSOne Partner`;
    pageContext.createPopupPage('sync-fwd-customer', popupLabel, createAppPage, { size: 'lg', backdrop: 'static' });
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let customerPartner = dRecord.record;
    let { appContext, pageContext, partnerType } = this.props;

    let writeCap = pageContext.hasUserWriteCapability();

    let param: any = { id: customerPartner.id };
    appContext
      .createHttpBackendCall("BFSOnePartnerService", "getBFSOnePartner", param)
      .withSuccessData((data: any) => {
        let observer = new entity.ComplexBeanObserver(data);

        const onPostCommit = (_entity: any, uiEditor?: app.AppComponent) => {
          this.reloadData();
          uiEditor?.props.pageContext.back();
        }

        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (<UIBFSOneCustomer appContext={appCtx} pageContext={pageCtx} observer={observer} readOnly={!writeCap}
            onPostCommit={onPostCommit} partnerType={partnerType} />);
        };
        let popupLabel: string = `${partnerType}: ${customerPartner.name}`;
        pageContext.createPopupPage('fwd-customer', popupLabel, createAppPage, { size: 'lg', backdrop: 'static' });
      })
      .call()
  }

  onNew = () => {
    let { pageContext, partnerType } = this.props;
    let observer = new entity.ComplexBeanObserver({});

    const pageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox' style={{ minHeight: 600 }}>
          <UINewBFSOnePartnerEditor
            appContext={appCtx} pageContext={pageCtx} observer={observer} partnerType={partnerType}
            onPostCommit={(_bean) => {
              pageCtx.back();
              this.onAddOrModifyDBRecordCallback(_bean);
              this.getVGridContext().getVGrid().forceUpdateView();
            }} />
        </div>
      )
    }
    pageContext.createPopupPage('new-partner', `New ${partnerType}`, pageContent, { size: 'flex-lg', backdrop: 'static' });
  }

  onDeleteAction(): void {
    const { appContext, plugin } = this.props;
    const selectedIds = plugin.getListModel().getSelectedRecordIds();

    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("warning"), T("No Customer Selected!"));
      return;
    }

    appContext.createHttpBackendCall('BFSOnePartnerService', 'deleteBFSOnePartners', { targetIds: selectedIds })
      .withSuccessData((_data: any) => {
        this.reloadData();
      })
      .withEntityOpNotification('delete', 'Partner')
      .call();
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    plugin.searchParams = searchParam;
    this.reloadData();
    this.nextViewId();
    // this.forceUpdate();
    this.vgridContext.getVGrid().forceUpdateView();
  };

  render() {
    if (this.isLoading()) return this.renderLoading();
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1 mt-1'>

          <div className='flex-hbox justify-content-start align-items-center'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={searchParam} field={"maxReturn"}
                options={[1000, 2000, 5000, 10000]}
                optionLabels={['Show 1000 records', 'Show 2000 records', 'Show 5000 records', 'Show 10.000 records']}
                onInputChange={this.onModify} />
            </div>
            <WRateFinderGridFilter context={this.vgridContext} />
          </div>

          <div className="flex-hbox justify-content-end align-items-center flex-grow-1">
            <bs.Button laf='warning' className="border-0 p-1 border-end rounded-0" outline
              onClick={() => this.onDeleteAction()}>
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button>
          </div>

        </div>
        <div key={this.viewId} className='flex-vbox'>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }
}

