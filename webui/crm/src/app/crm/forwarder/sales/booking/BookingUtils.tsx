import React from 'react'

import { app, bs, entity } from '@datatp-ui/lib';
import { app as lgc_app } from '@datatp-ui/logistics';

import { UIBookingModel } from './UIBookingInfo';

import { BookingModelBuilder } from '.';
import { UISpecificQuotationEditor } from '../quotation/specific/UISpecificQuotation';

import mapToTypeOfShipment = lgc_app.logistics.settings.mapToTypeOfShipment;
import TransportationTool = lgc_app.logistics.settings.TransportationTool;
import TransportationMode = lgc_app.logistics.settings.TransportationMode;

export function calculateSellingRate(sellingRate: any): any {
  sellingRate.unitPrice = sellingRate.unitPrice || 0;
  sellingRate.taxRate = sellingRate.taxRate || 0;
  sellingRate.exchangeRate = sellingRate.exchangeRate || 0;
  sellingRate.quantity = sellingRate.quantity || 0;
  const total = sellingRate.unitPrice * sellingRate.quantity;
  const totalTax = total * sellingRate.taxRate;
  sellingRate.totalAmount = total + totalTax;
  sellingRate.domesticUnitPrice = sellingRate.unitPrice * sellingRate.exchangeRate;
  sellingRate.domesticTotalAmount = (total + totalTax) * sellingRate.exchangeRate;
  sellingRate.domesticCurrency = 'VND';
  return sellingRate;
}

export class UIBookingUtils {

  static onNewBooking = (ui: UISpecificQuotationEditor, quotation: any) => {
    let { pageContext, appContext } = ui.props;

    let quoteList: any[] = quotation['quoteListSelector'] || [];

    if (quoteList.length === 0) {
      quoteList = quotation['quoteList'] || [];
      appContext.addOSNotification('info', "The first quote is selected by default");
    }

    if (quoteList.length === 0) {
      let message = (<div className="ms-1 text-info py-3 border-bottom">Must be select one freight rate.!!!</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return;
    }

    const quote: any = quoteList[0];


    const inquiry: any = quotation['inquiry'] || {};
    const weight: number = inquiry['chargeableWeight'] || inquiry['grossWeightKg'] || 1;
    const volume: number = inquiry['chargeableVolume'] || inquiry['volumeCbm'] || 1;

    const type = mapToTypeOfShipment(inquiry['purpose'], inquiry['mode'])
    const bookingBuilder = new BookingModelBuilder(inquiry);
    bookingBuilder.processQuote(quote);

    quotation['localHandlingCharges'].map((sel: any) => {

      if (sel['unit'] === 'CBM') {
        sel['quantity'] = volume;
      } else if (sel['unit'] === 'KGS') {
        sel['quantity'] = weight;
      } else {
        sel['quantity'] = 1;
      }

      let mode: TransportationMode = sel['mode'];
      if (TransportationTool.isTruck(mode) || TransportationTool.isUnknown(mode)) {
        bookingBuilder.buildLocalCharge('CUSTOM', sel, inquiry['containers'] || []);
      } else {
        bookingBuilder.buildLocalCharge('LOCAL_CHARGE', sel, inquiry['containers'] || []);
      }
    })
    bookingBuilder.buildShipmentInfo();
    let newBookingModel = bookingBuilder.buildBookingModel();

    appContext.createHttpBackendCall('BookingService', 'newBookingModel', { model: newBookingModel })
      .withSuccessData((data: any) => {
        console.log(data);
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIBookingModel appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver(data)} />
          );
        }
        pageContext.createPopupPage('new-booking-page', `Send IBooking (${type})`, createAppPage, { size: 'xl', backdrop: "static" });
      })
      .call()
  }
}

