import React from 'react';
import * as FeatherIcon from 'react-feather';

import { util, app, entity, grid, bs, sql, input } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";
import { app as lgc_app } from '@datatp-ui/logistics';

import { T } from "../backend";
import { UIBookingModel } from './UIBookingInfo';
import { WRateFinderGridFilter } from '../../price';

import _settings = lgc_app.logistics.settings;

import TransportationMode = _settings.TransportationMode;
import mapToTypeOfShipment = _settings.mapToTypeOfShipment;
import TransportationTool = _settings.TransportationTool;

import BBRefPartner = module.partner.BBRefPartner;

const SESSION = app.host.DATATP_SESSION;

export class UIBookingListPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      context: 'company',
      service: 'BookingService',
      searchMethod: 'searchBookings',
      changeStorageStateMethod: 'changeBookingStorageState'
    }

    const today = new Date();
    let bookingDateRange = new util.TimeRange();
    bookingDateRange.fromSetDate(today).fromSubtract(2, "month").fromStartOf("month"); // -3 months
    bookingDateRange.toSetDate(today).toEndOf("month");

    this.searchParams = {
      params: {
        "space": space,
      },
      filters: sql.createSearchFilter(),
      optionFilters: [
        sql.createStorageStateFilter(
          [entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED]
        ),
      ],
      rangeFilters: [
        ...sql.createDateTimeFilter("bookingDate", T("Booking Date"), bookingDateRange),
      ],
      maxReturn: 1000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

}

interface UIBookingListFilterProps extends sql.SearchParamsProps {
  context: grid.VGridContext;
}
export class UIBookingListFilter extends sql.BaseSearchParams<UIBookingListFilterProps> {
  renderCustomOptionFilter() {
    return (
      <div>
        {this.renderOptionFilter(this.getOptionFilter('storageState'))}
      </div>
    )
  }

  render() {
    let { searchParams, context, onSubmit } = this.props;
    let uiList = context.uiRoot as UIBookingList
    let { appContext, pageContext } = uiList.props
    let param = searchParams['params']
    let defaultFilter = this.getFilter("search");
    let validDateFilter = this.getRangeFilter('bookingDate');
    let smallScreen = bs.ScreenUtil.isSmallScreen();
    let popoverWidth = 520;
    if (smallScreen) {
      popoverWidth = 350;
    }

    let html = (
      <div className="flex-hbox justify-content-between">
        <grid.WGridFilter context={context} />
        <div className='flex-hbox flex-grow-0'>
          <div className="flex-hbox-grow-0" >
            {this.renderFilter(defaultFilter, "Search Term")}
          </div>
          <bs.OffCanvas className="flex-hbox-grow-0" offcanvasId={this.popoverId}>
            <bs.OffCanvasToggle laf="primary" className="flex-hbox align-items-center">
              <FeatherIcon.Search size={12} /><FeatherIcon.ChevronLeft size={12} />
            </bs.OffCanvasToggle>
            <bs.OffCanvasContent className="vh-100 px-1" placement={'end'} style={{ width: popoverWidth }}>
              <h3 className="border-bottom mb-2 py-1">DB Search</h3>
              <div className="flex-vbox-grow-0 mb-3">
                <input.BBStringField label={T('Search Term')} bean={defaultFilter} field={"filterValue"} placeholder={T("Search pattern...")} />
              </div>
              <bs.GreedyScrollable>
                <div className='flex-vbox mt-2'>
                  <BBRefPartner appContext={appContext} pageContext={pageContext}
                    label='Client' placeholder='Client'
                    bean={param} beanIdField="clientPartnerId" beanLabelField="clientLabel" />
                </div>
                <div className='flex-vbox mt-2'>
                  {this.renderDateRangeFilter(validDateFilter)}
                  {this.renderCustomOptionFilter()}
                  {this.renderOrderBy()}
                  {this.renderMaxReturn()}
                </div>
              </bs.GreedyScrollable>
              <div className="flex-vbox-grow-0 py-3">
                <div className='flex-hbox justify-content-center'>
                  <bs.Button laf="primary" className="px-4 py-2" onClick={() => onSubmit(searchParams)}>
                    <FeatherIcon.Search size={12} />Search
                  </bs.Button>
                </div>
              </div>
            </bs.OffCanvasContent>
          </bs.OffCanvas>
        </div>
      </div>
    );
    return html;
  }
}

interface UIBookingListProps extends entity.DbEntityListProps { }
export class UIBookingList extends entity.DbEntityList<UIBookingListProps> {
  createVGridConfig(): grid.VGridConfig {
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 45,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'referenceCode', label: T('Ref.'), hint: 'Ref', width: 130,
            container: 'fixed-left', state: { showRecordState: true }, filterable: true,
            fieldDataGetter: (record: any) => {
              return record.referenceCode || record.bookingCaseReference || "N/A";
            },
            onClick: function (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) {
              let uiRoot = ctx.uiRoot as UIBookingList
              uiRoot.onSelect(dRecord);
            },
          },
          {
            name: 'externalCaseReference', label: T('BFSOne Ref.'), hint: 'BFSOne Ref', width: 150,
            filterable: true, container: 'fixed-left', state: { showRecordState: true },
            fieldDataGetter: (record: any) => {
              return record.externalCaseReference || "N/A";
            },
          },
          { name: 'hawbNo', label: T('Hawb No.'), width: 150, filterable: true, },
          {
            name: 'clientLabel', label: T('Customer Name'), width: 350,
            filterableType: 'string', filterable: true, sortable: true,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let valTruncate = util.text.formater.uiTruncate(dRec.record[field.name], 340, true);
              return <div className="flex-hbox pe-1 me-1">{valTruncate}</div>
            }
          },
          {
            name: 'typeOfShipment', label: T('Type of shpt'), width: 130,
            filterable: true, filterableType: 'string',
            fieldDataGetter(record: any) {
              return mapToTypeOfShipment(record['purpose'], record['mode'])
            },
          },
          {
            name: 'containerTypes', label: T(`Vol`), width: 170,
            fieldDataGetter(record: any) {
              let val: string = record['containerTypes'];
              if (val) return val;
              let mode: TransportationMode = record['mode']
              if (TransportationTool.isAir(mode)) return record['grossWeightKg'] + ' KGS'
              if (TransportationTool.isSeaLCL(mode)) return record['volumeCbm'] + ' CBM'
            },
          },
          { name: 'incoterms', label: T('Term'), width: 100 },
          {
            name: 'fromLocationLabel', label: T('Port of Loading'), width: 200, cssClass: 'pe-1',
            filterableType: 'string', filterable: true, sortable: true,
          },
          {
            name: 'toLocationLabel', label: T('Port of Discharge'), width: 200, cssClass: 'pe-1',
            filterableType: 'string', filterable: true, sortable: true,
          },
          {
            name: 'bookingDate', label: 'Booking Date', filterableType: 'Date', filterable: true,
            width: 120, cssClass: 'text-right', format: util.text.formater.compactDate
          },
          {
            name: "salemanLabel", label: 'Saleman.', width: 250,
            filterableType: 'string', filterable: true, sortable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;
              let employeeName: string = record['salemanLabel'] || 'N/A';
              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{employeeName}</div>
                </div>
              )
            },
          },
          {
            name: "receiverLabel", label: 'Receiver.', width: 250,
            filterableType: 'string', filterable: true, sortable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;
              let employeeName: string = record['receiverLabel'] || 'N/A';
              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['receiverAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{employeeName}</div>
                </div>
              )
            },
          },
          {
            name: 'cargoReadyDate', label: T('CRD.'), hint: 'Cargo Ready Date', width: 170, state: { visible: false },
            format: util.text.formater.compactDate
          },
          {
            name: 'estimatedTimeDeparture', label: T('ETD'), width: 140, state: { visible: false },
            format: util.text.formater.compactDate
          },
          {
            name: 'descOfGoods', label: T('Desc Of Goods.'), width: 250,
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record = dRecord.record;
              let val = record['descOfGoods'] || '-'
              return (
                <div className="flex-hbox justify-content-between align-items-center px-1">
                  <div className="flex-hbox justify-content-center pe-1 me-1">{val}</div>
                  {/* <FeatherIcon.Info size={12} className='text-info' onClick={() => alert('TODO')} /> */}
                </div>
              )
            },
          },
          {
            name: 'modifiedTime', label: T('Modified Date'), width: 170, container: 'fixed-right',
            filterableType: 'Date', filterable: true, format: util.text.formater.compactDateTime,
          },

        ],
      },
      toolbar: {
        hide: true
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    };
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let booking = dRecord.record;

    let { appContext, pageContext } = this.props;

    const accessAccountId = SESSION.getAccountId();

    if (accessAccountId === booking['salemanAccountId'] || pageContext.hasUserModeratorCapability()) {
      let writeCap = pageContext.hasUserWriteCapability();
      appContext.createHttpBackendCall("BookingService", "getBookingModel", { bookingId: booking['id'] })
        .withSuccessData((bookingModel: any) => {
          let referenceCode: string = bookingModel['referenceNo'] || bookingModel['bookingNo']
          const createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (
              <UIBookingModel appContext={appCtx} pageContext={pageCtx} readOnly={!writeCap}
                observer={new entity.ComplexBeanObserver(bookingModel)} />
            );
          }
          pageContext.addPageContent(`booking-${referenceCode}`, `BK: ${referenceCode}`, createPageContent);
        })
        .call();
    } else {
      let message = (
        <div className="ms-1 text-warning py-3 border-bottom">
          Access denied. This booking belongs to another salesperson.
        </div>
      );
      bs.dialogShow('Access Restricted', message, { backdrop: 'static', size: 'md' });
    }
  }

  onDeleteAction(): void {
    const { appContext, plugin, pageContext } = this.props;
    const bookings = plugin.getListModel().getSelectedRecords();

    const accessAccountId = SESSION.getAccountId();
    let isOwner = bookings.every(record => record.salemanAccountId === accessAccountId);

    if (isOwner || pageContext.hasUserModeratorCapability()) {
      const bookingIds: any = bookings.map(booking => booking.id);
      const onConfirmDelete = () => {
        appContext.createHttpBackendCall('BookingService', 'deleteBookings', { bookingIds: bookingIds })
          .withSuccessData((_data: any) => {
            appContext.addOSNotification("success", T(`Delete Success`));
            plugin.getListModel().removeSelectedDisplayRecords();
            this.vgridContext.model.removeSelectedDisplayRecords();
            this.getVGridContext().getVGrid().forceUpdateView();
          })
          .call();
      };
      let messageEle = (<div className="text-danger">Do you want to delete these records?</div>);
      bs.dialogConfirmMessage(T("Confirm Delete"), messageEle, onConfirmDelete);
    } else {
      let message = (
        <div className="ms-1 text-warning py-3 border-bottom">
          Access denied. You can only delete your own bookings.
        </div>
      );
      bs.dialogShow('Access Restricted', message, { backdrop: 'static', size: 'md' });
    }
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    plugin.searchParams = searchParam;
    this.reloadData();
    this.nextViewId();
    // this.forceUpdate();
    this.vgridContext.getVGrid().forceUpdateView();
  };


  render() {
    if (this.isLoading()) return this.renderLoading();
    const { plugin, pageContext } = this.props;
    let searchParam = plugin.getSearchParams();
    let writeCap = pageContext.hasUserWriteCapability();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1 mt-1'>

          <div className='flex-hbox justify-content-start align-items-center'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={searchParam} field={"maxReturn"}
                options={[1000, 2000, 5000, 10000]}
                optionLabels={['Show 1000 records', 'Show 2000 records', 'Show 5000 records', 'Show 10.000 records']}
                onInputChange={this.onModify} />
            </div>
            <WRateFinderGridFilter context={this.vgridContext} />
          </div>

          <div className="flex-hbox justify-content-end align-items-center flex-grow-1">
            <bs.Button laf='warning' className="border-0 p-1 border-end rounded-0" outline
              onClick={() => this.onDeleteAction()} hidden={!writeCap}>
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button>
          </div>
        </div>
        <div key={this.viewId} className='flex-vbox'>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }

}

