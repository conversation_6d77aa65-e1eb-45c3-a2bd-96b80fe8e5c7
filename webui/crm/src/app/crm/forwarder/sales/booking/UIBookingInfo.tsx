import React from 'react';
import * as FeatherIcon from 'react-feather'

import { app, bs, input, entity, server } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";
import { app as lgc_app } from '@datatp-ui/logistics';

import { T } from '../backend';

import { UISpecificInquiryForm } from '../inquiry';
import { UIBookingChargeListEditor } from './UIBookingChargeListEditor';
import { CalculatorProvider, createCalculatorContext } from '../calculator';
import { calculateSellingRate } from './BookingUtils';
import { CustomerEntityUtil } from '../common';
import { BBRefBFSOnePartner } from '../../bfsone';

import _settings = lgc_app.logistics.settings;

import FreightTerm = _settings.FreightTerm;
import ImportExportPurpose = _settings.ImportExportPurpose;
import mapToTypeOfShipment = _settings.mapToTypeOfShipment;
import TransportationMode = _settings.TransportationMode;
import TransportationTool = _settings.TransportationTool;

import BBRefEmployee = module.company.hr.BBRefEmployee;

class UIBookingModelFormInfo extends entity.AppDbComplexEntity {
  displayService: string = '';

  constructor(props: entity.AppDbComplexEntityProps) {
    super(props);
    const { observer } = this.props;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    let purpose: ImportExportPurpose = inquiry['purpose']
    let mode: TransportationMode = inquiry['mode']
    this.displayService = mapToTypeOfShipment(purpose, mode);
  }

  onModify = (_booking: any, field: string, oldVal: any, newVal: any) => {
    const { observer, onModify } = this.props;
    if (field.length > 0) {
      observer.replaceBeanProperty(field, newVal);
    } else {
      observer.setMutableBean(_booking);
    }
    if (onModify) {
      onModify(observer.getMutableBean(), field, oldVal, newVal);
    } else {
      this.forceUpdate();
    }
  }

  onShowMoreInfo = () => {
    const { pageContext, observer } = this.props;
    let inquiryOb: any = observer.createComplexBeanObserver('inquiry', {});
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox' style={{ height: 600 }}>
          <UISpecificInquiryForm appContext={appCtx} pageContext={pageCtx} observer={inquiryOb} />
        </div>
      );
    }
    let dialogConfig: bs.DialogOptions = { size: 'lg', backdrop: 'static' };
    pageContext.createPopupPage('inquiry-detail', T('Inquiry Detail'), createAppPage, dialogConfig);
  }

  onPostSelectPartner = (_bean: any, selectBean: any = {}) => {
    const { observer } = this.props;
    let inquiry: any = observer.getComplexBeanProperty('inquiry', {});

    const oldClientPartnerId = inquiry['clientPartnerId'];
    const newClientPartnerId = selectBean['id'];
    const newClientName = selectBean['name'];

    let sellingRates = observer.getComplexArrayProperty('sellingRates', []);
    let updatedSellingRates = sellingRates.map((item: any) => {
      if (!item.payerPartnerId || item.payerPartnerId === oldClientPartnerId) {
        return {
          ...item,
          payerPartnerId: newClientPartnerId,
          payerPartnerCode: selectBean['bfsonePartnerCode'],
          payerPartnerLabel: newClientName
        };
      }
      return item;
    });

    observer.replaceBeanProperty('sellingRates', updatedSellingRates);
    inquiry['clientPartnerId'] = newClientPartnerId
    inquiry['clientLabel'] = newClientName
    inquiry['attention'] = selectBean['personalContact'];

    let purpose: ImportExportPurpose = inquiry['purpose'] || ImportExportPurpose.EXPORT;
    _bean['clientLabel'] = newClientName
    _bean['clientPartnerId'] = newClientPartnerId
    if (purpose === ImportExportPurpose.EXPORT) {
      _bean['shipmentInfo']['shipperLabel'] = selectBean['printCustomConfirmBillInfo'] ? selectBean['printCustomConfirmBillInfo'] : selectBean['address']
    } else {
      _bean['shipmentInfo']['consigneeLabel'] = selectBean['printCustomConfirmBillInfo'] ? selectBean['printCustomConfirmBillInfo'] : selectBean['address']
    }
    this.onModify(_bean, '', null, null);
  }

  onCheckHawb = (_wInput: input.WInput, _bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { appContext } = this.props;
    if (newVal) {
      appContext.createHttpBackendCall('BFSOneCRMService', 'findBookingLocalByHawb', { hawb: newVal })
        .withSuccessData((bookingLocals: any[] = []) => {
          if (bookingLocals && bookingLocals.length > 0) {
            _bean[_field] = _oldVal;
            bs.dialogShow('Warning',
              <div className="text-warning fw-bold text-center py-3 border-bottom">
                <FeatherIcon.AlertCircle className="mx-2" />{T(`The HWBNO: ${newVal} was existed on Internal Booking , please type new HWBNO to continue !`)}
              </div>,
              { backdrop: 'static', size: 'sm' }
            );
            this.forceUpdate();
          }
        })
        .withFail((response: server.BackendResponse) => {
          let title = T('Check Hawb No Failed!');
          let message = response.error.message || '';
          bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
          return;
        })
        .call();
    }
  }

  render() {
    const { appContext, pageContext, observer } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();

    let bookingModel = observer.getMutableBean();
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    let shipmentInfo = observer.getComplexBeanProperty('shipmentInfo', {});
    let mode = inquiry.mode;

    return (
      <div className="flex-vbox rounded bg-white px-2 py-1" style={{
        padding: '1rem',
        border: '1px solid var(--phoenix-border-color)',
        borderRadius: '0.5rem',
        background: 'linear-gradient(to right, #fcfcff, #ffffff)',
        boxShadow: '0 0 10px rgba(0,0,0,0.02)'
      }}>

        {/* TODO: Dan - show when page is breakcumbs */}
        {/* <div className="d-flex align-items-center justify-content-between bg-soft-primary rounded-top"
          style={{
            padding: '0.5rem 0.75rem',
            borderBottom: '1px solid var(--phoenix-border-color)',
            background: 'var(--phoenix-card-bg)'
          }}>
          <div className="d-flex align-items-center gap-2">
            <h5 className="mb-0" style={{ color: '#344767', fontWeight: 600 }}>  {`General (${this.displayService})`}</h5>
          </div>
          <button type="button"
            className="btn btn-link text-primary p-0 d-flex align-items-center"
            style={{ fontSize: '0.85rem' }}
            onClick={this.onShowMoreInfo}>
            Details
            <FeatherIcon.ChevronRight size={14} style={{ marginTop: '1px' }} />
          </button>
        </div> */}

        <div className='flex-grow-0'>
          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                label={T('Reference No.')} bean={bookingModel} field={'referenceNo'} disable />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField label={T('B/K No.')} bean={bookingModel} field={'bookingNo'} disable={!writeCap}
                placeholder='Enter Carrier B/K No.' />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBSelectField
                label={T('Shipment Type')} bean={bookingModel} field={"shipmentType"}
                options={['FREE-HAND', 'NOMINATED']} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBSelectField
                label={T('Payment Term')} bean={bookingModel} field={"paymentTerm"}
                options={[FreightTerm.PREPAID, FreightTerm.COLLECT]} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField label={T('M-B/L No.')} bean={shipmentInfo} field={'planMBCode'} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField label={T('H-B/L No.')} bean={shipmentInfo} field={'planHBCode'} disable={!writeCap}
                onBgInputChange={this.onCheckHawb} />
            </bs.Col>

            <bs.Col lg={3}>
              <input.BBDateTimeField
                className='rdt-bottom' bean={shipmentInfo} label={T('Date of Arrival')}
                field={'planTimeArrival'} dateFormat={"DD/MM/YYYY"} timeFormat={false} disable={!writeCap} />
            </bs.Col>
            <bs.Col lg={3}>
              <input.BBDateTimeField
                className='rdt-bottom' label={T('Date of Loading')}
                field={'planTimeDeparture'} bean={shipmentInfo} dateFormat={"DD/MM/YYYY"}
                timeFormat={false} disable={!writeCap} />
            </bs.Col>

          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <BBRefBFSOnePartner placeholder='Client...' label='Client' required inputObserver={observer}
                appContext={appContext} pageContext={pageContext} disable={!writeCap} minWidth={350}
                bean={bookingModel} beanIdField={'clientPartnerId'} beanLabelField={'clientLabel'} partnerType='Customer'
                hideMoreInfo
                onPostUpdate={(input: any, bean: any, selectOpt: any) => this.onPostSelectPartner(bean, selectOpt)} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBNumberField precision={3} maxPrecision={3}
                bean={bookingModel} label={T("Gross Weight (KGS)")} field={'grossWeight'} disable={!writeCap}
                onInputChange={this.onModify} />
            </bs.Col>

            <bs.Col lg={3}>
              <input.BBNumberField precision={3} maxPrecision={3}
                bean={bookingModel} label={T("Volume (CBM)")} field={'volume'} disable={!writeCap}
                onInputChange={this.onModify} />
            </bs.Col>

            <bs.Col lg={3}>
              {
                TransportationTool.isSeaFCL(mode) ? (
                  <input.BBStringField label={T('Container Types')} disable bean={bookingModel} field={'unitOfPackage'} />
                ) : TransportationTool.isAir(mode) ? (
                  <div className="d-flex gap-2">
                    <div className="flex-grow-1">
                      <input.BBNumberField precision={3} maxPrecision={3} label={T('C.W (KGS)')} bean={inquiry} field={'chargeableWeight'}
                        disable={!writeCap} onInputChange={this.onModify} />
                    </div>
                    <div className="flex-grow-1">
                      <input.BBNumberField label={T('Packages')} bean={bookingModel} field={'packages'} disable={!writeCap} />
                    </div>
                  </div>
                ) : (
                  <input.BBNumberField label={T('Packages')} bean={bookingModel} field={'packages'} disable={!writeCap} />
                )
              }
            </bs.Col>
          </bs.Row>
        </div>
      </div>
    );
  }
}

class UIBookingModelEditor extends entity.AppDbComplexEntityEditor {

  state = {
    isSending: false
  };

  onModify = (booking: any, field: string, oldVal: any, newVal: any) => {
    const { observer, onModify } = this.props;
    if (field) observer.replaceBeanProperty(field, newVal);
    else observer.setMutableBean(booking);

    if (field === 'grossWeight' || field === 'volume' || field === 'chargeableWeight') {
      let sellingRates = observer.getComplexArrayProperty('sellingRates', []);
      let updateSellingRates: any[] = [];

      for (let record of sellingRates) {
        const unit = (record.unit || '').trim();
        if ((field === 'grossWeight' || field === 'chargeableWeight') &&
          (unit === 'KGS' || unit === 'KGM' || unit === 'KG')) {
          record.quantity = newVal;
        } else if (field === 'volume' && unit === 'CBM') {
          record.quantity = newVal;
        } else {
          record.quantity = 1;
        }
        updateSellingRates.push(calculateSellingRate(record));
      }
      observer.replaceBeanProperty('sellingRates', updateSellingRates);
    }

    this.nextViewId();
    if (onModify) onModify(observer.getMutableBean(), field, oldVal, newVal);
    else this.forceUpdate();
  }

  onCreateBFSIBooking = () => {
    let { observer, appContext } = this.props;
    let bookingModel = observer.commitAndGet();

    let sellingRates = observer.getComplexArrayProperty('sellingRates', []);
    // Validate note length for each selling rate
    const MAX_NOTE_LENGTH = 500;
    const invalidNotes = sellingRates.filter((rate: any) =>
      rate.note && rate.note.length > MAX_NOTE_LENGTH
    );

    if (invalidNotes.length > 0) {
      bs.dialogShow('Warning',
        <div className="text-warning fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {T('Some selling rates have notes exceeding 1000 characters. Please shorten them.')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Selling rates notes exceed maximum length');
    }

    if (!bookingModel['clientPartnerId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide a valid Client.')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Please provide a valid Client.')
    }

    if (!bookingModel['receiverAccountId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide a valid Receiver.')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Please provide a valid Receiver.')
    }

    if (!bookingModel['fromLocationCode'] || !bookingModel['toLocationCode']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide both Port Of Loading and Port Of Discharge.')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Please provide both Port Of Loading and Port Of Discharge.')
    }

    this.setState({ isSending: true });

    appContext.createHttpBackendCall('BFSOneCRMService', 'quickSendBFSOneIBooking', { 'bookingModel': bookingModel })
      .withSuccessData((iBooking: any) => {
        console.log('------------- Booking ------------------');
        console.log(iBooking);
        if (iBooking['referenceNo']) {
          observer.replaceWith(iBooking)
        }
        this.setState({ isSending: false });
        this.nextViewId();
        this.forceUpdate();
      })
      .withEntityOpNotification('commit', "IBooking (BFSOne)")
      .withFail((response: server.BackendResponse) => {
        let title = T('IBooking (BFSOne)');
        let message = response.error.message || '';
        bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        this.setState({ isSending: false });
        // return;
      })
      .call();
  }

  onPreCommit = (observer: entity.ComplexBeanObserver) => {

    let bookingModel = observer.getMutableBean();
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    if (!bookingModel['clientPartnerId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide a valid Client.')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Please provide a valid Client.')
    }

    let gw: number = bookingModel['grossWeight'] || 0;
    let cbm: number = bookingModel['volume'] || 0;
    let containerTypes: string = bookingModel['unitOfPackage'] || '';
    let mode: TransportationMode = inquiry['mode'];

    if (TransportationTool.isSeaFCL(mode) && !containerTypes) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide Container Types.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    } else if (TransportationTool.isAir(mode) || TransportationTool.isSeaLCL(mode)) {
      if (gw + cbm === 0) {
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide GrossWeight/ CBM.')}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        return;
      }
    }

  }

  onModifySellingRates = (beans: any | Array<any>, _action?: entity.ModifyBeanActions) => {
    const { observer } = this.props;
    observer.replaceBeanProperty('sellingRates', beans);
  }

  render() {
    const { appContext, pageContext, observer } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let bookingModel = observer.getMutableBean();

    return (
      <div className='flex-vbox' >

        <bs.HSplit updateOnResize>
          <bs.HSplitPane title={T('General Info')} heightGrow={0} className='flex-grow-0'>
            <UIBookingModelFormInfo {...this.props} onModify={this.onModify} />
          </bs.HSplitPane>

          <bs.HSplitPane title={T('Selling Rates')} className='py-0'>

            <div className='flex-vbox' style={{ flex: 1, overflow: 'auto' }} key={this.viewId}>
              <UIBookingChargeListEditor
                plugin={observer.createVGridEntityListEditorPlugin('sellingRates', [])}
                appContext={appContext} pageContext={pageContext} bookingModel={bookingModel}
                editorTitle={T('Selling Rates')} dialogEditor={true} onModifyBean={this.onModifySellingRates} />
            </div>

            <div className="bg-light border rounded" style={{ padding: '0 0.75rem' }}>
              <div className="d-flex gap-2">
                <div className="flex-grow-1 d-flex align-items-center gap-2">
                  <label className="form-label mb-0 text-nowrap" style={{ fontSize: '0.775rem', fontWeight: 700, color: '#495057' }}>
                    <FeatherIcon.User size={13} className="text-primary me-1" />{T('Sender')}
                  </label>
                  <BBRefEmployee className='form-control-sm flex-grow-1' minWidth={400} placement='top-start' hideMoreInfo
                    appContext={appContext} pageContext={pageContext} bean={bookingModel} disable inputObserver={observer}
                    beanIdField='senderAccountId' beanLabelField='senderLabel' placeholder='Select Saleman...'
                    onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                      bean['senderAccountId'] = selectOpt['accountId'];
                      bean['senderLabel'] = selectOpt['label'];
                      this.onModify(bean, 'senderAccountId', null, selectOpt['accountId'])
                    }} />
                </div>

                <div className="flex-grow-1 d-flex align-items-center gap-2">
                  <label className="form-label mb-0 text-nowrap" style={{ fontSize: '0.775rem', fontWeight: 700, color: '#495057' }}>
                    <FeatherIcon.Users size={13} className="text-warning me-1" />{T('Receiver')}
                  </label>
                  <BBRefEmployee className='form-control-sm flex-grow-1' minWidth={400} placement='top-start' hideMoreInfo
                    appContext={appContext} pageContext={pageContext} bean={bookingModel} disable={!writeCap} inputObserver={observer}
                    allCompany
                    beanIdField='receiverAccountId' beanLabelField='receiverEmployeeLabel' placeholder='Select CS/DOCS...'
                    onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                      bean['receiverAccountId'] = selectOpt['accountId'];
                      bean['receiverEmployeeLabel'] = selectOpt['label'];
                      this.onModify(bean, 'receiverAccountId', null, selectOpt['accountId'])
                    }} />
                </div>
              </div>
            </div>

          </bs.HSplitPane>

        </bs.HSplit>
        <bs.Toolbar className='border'>

          <entity.ButtonEntityCommit btnLabel='Draft IB' className='flex-hbox-grow-0 justify-content-center align-items-center me-2'
            appContext={appContext} pageContext={pageContext} hide={!pageContext.hasUserModeratorCapability()} observer={observer}
            commit={{ entityLabel: 'Booking', context: 'company', service: 'BookingService', commitMethod: 'saveBookingModel' }}
            onPreCommit={this.onPreCommit}
            onPostCommit={this.onPostCommit} />

          <bs.Button laf='primary' className='flex-hbox-grow-0 justify-content-center align-items-center me-2'
            hidden={!writeCap}
            onClick={this.onCreateBFSIBooking} disabled={this.state.isSending}>
            <FeatherIcon.Plus size={12} />{this.state.isSending ? 'Sending...' : T('Send IBooking (BFSOne)')}
          </bs.Button>

        </bs.Toolbar>
      </div>
    );
  }
}

export class UIBookingModel extends entity.AppDbComplexEntityEditor {

  render(): React.ReactNode {
    let { observer } = this.props;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    let chargeMargin = CustomerEntityUtil.createDefaultCustomerChargeModel();
    let calContext = createCalculatorContext(chargeMargin.transportChargeMargin, inquiry);

    return (
      <CalculatorProvider initContext={calContext}>
        <UIBookingModelEditor {...this.props} />
      </CalculatorProvider>
    )
  }
}
