import { util } from '@datatp-ui/lib';

import { FreightTerm } from "../../model";
import { app as lgc_app } from '@datatp-ui/logistics';

import { InquiryUtil } from "../inquiry/InquiryUtils";
import { ContainerTypeUnit } from '../../common/ContainerTypeUtil';

import TransportationTool = lgc_app.logistics.settings.TransportationTool;
import TransportationMode = lgc_app.logistics.settings.TransportationMode;
import TimeUtil = util.TimeUtil;

export class BookingModelBuilder {
  private inquiry: any;

  private seaQuote: any;
  private airQuote: any;

  private shipmentInfo: any;
  private sellingRates: any[] = [];

  constructor(inquiry: any) {
    this.inquiry = inquiry;
  }

  public buildShipmentInfo() {
    this.shipmentInfo = {
      planTimeArrival: this.inquiry.cargoReadyDate,
      planTimeDeparture: this.inquiry.cargoReadyDate,
    };

    let quote = this.seaQuote || this.airQuote || {};
    if (quote['carrierPartnerId']) {
      this.shipmentInfo['carrierPartnerId'] = quote['carrierPartnerId'];
      this.shipmentInfo['carrierLabel'] = quote['carrierLabel'];
    }
    if (quote['handlingAgentPartnerId']) {
      this.shipmentInfo['handlingAgentPartnerId'] = quote['handlingAgentPartnerId'];
      this.shipmentInfo['handlingAgentPartnerLabel'] = quote['handlingAgentPartnerLabel'];
    }
  }

  public processQuote(quote: any): void {
    let mode: TransportationMode = quote['mode'];
    if (TransportationTool.isSea(mode)) {
      this.processSeaQuote(quote);
    } else if (TransportationTool.isAir(mode)) {
      this.processAirQuote(quote);
    }
  }

  public processSeaQuote(seaQuote: any): void {
    this.seaQuote = seaQuote;
    let containers: any[] = this.inquiry.containers || [];
    let ofTemplate: any = this.buildSeaFreightTemplate(seaQuote);

    if (TransportationTool.isSeaFCL(seaQuote.mode)) {
      for (let container of containers) {
        let qty = container.quantity || 0;
        if (qty > 0) {
          let containerType = ContainerTypeUnit.match(container.containerType);
          if (containerType) {
            let priceLevel = containerType.toFCLPriceLevel();
            if (priceLevel) {
              let cloneFreight = { ...ofTemplate };
              cloneFreight.quantity = qty;
              cloneFreight.unit = containerType.label;
              cloneFreight.unitPrice = seaQuote.priceGroup[priceLevel] || 0;
              cloneFreight.totalAmount = cloneFreight.unitPrice * qty;
              this.sellingRates.push(cloneFreight);
            }
          }
        }
      }
    } else {
      let price = seaQuote.priceGroup['selectedPrice'] || 0;
      if (price > 0) {
        let qty = this.inquiry['chargeableVolume'] || this.inquiry['volumeCbm'] || 1;
        ofTemplate.quantity = qty;
        ofTemplate.unit = 'CBM';
        ofTemplate.unitPrice = seaQuote.priceGroup['selectedPrice'] || 0;
        ofTemplate.totalAmount = seaQuote.priceGroup['selectedPrice'] * qty;
        this.sellingRates.push(ofTemplate);
      }
    }
  }

  private buildSeaFreightTemplate(quote: any) {
    return {
      type: 'SEAFREIGHT',
      group: this.inquiry.mode,
      payerPartnerId: this.inquiry.clientPartnerId,
      payerPartnerLabel: this.inquiry.clientLabel,
      code: 'S_OF',
      name: 'SEAFREIGHT',
      taxRate: quote.taxRate,
      currency: quote.currency || "USD",
      note: quote['note'],
      domesticCurrency: 'VND',
      domesticUnitPrice: 0,
      domesticTotalAmount: 0
    };
  }

  public buildLocalCharge(type: 'LOCAL_CHARGE' | `CUSTOM`, item: any, containers: any[]) {
    let quoteRate: any = item['quoteRate'] || {}
    let unitPrice: number = item['unitPrice'] || 0

    if (unitPrice !== 0) {
      let qty: number = item['quantity'] || 1;
      let addCharge: any = {
        group: this.inquiry.mode,
        type: type,
        payerPartnerId: this.inquiry.clientPartnerId,
        payerPartnerLabel: this.inquiry.clientLabel,
        code: item.code,
        name: item.name,
        currency: item.currency || "USD",
        taxRate: item.taxRate || 0,
        unit: item.unit,
        quantity: qty,
        unitPrice: unitPrice,
        totalAmount: unitPrice * qty,
        note: item['note'],
        domesticCurrency: 'VND',
        domesticUnitPrice: 0,
        domesticTotalAmount: 0
      };
      this.sellingRates.push(addCharge)
    } else {
      for (let sel of containers) {
        let containerType = ContainerTypeUnit.match(sel['containerType']);
        if (containerType) {
          let unitPrice: number = quoteRate[containerType.label] || quoteRate[containerType.name] || 0;
          if (unitPrice === 0) {
            continue;
          }

          let qty: number = sel['quantity'] || 1;
          let addCharge: any = {
            group: this.inquiry.mode,
            type: type,
            payerPartnerId: this.inquiry.clientPartnerId,
            payerPartnerLabel: this.inquiry.clientLabel,
            code: item.code,
            name: item.name,
            currency: item.currency || "USD",
            taxRate: item.taxRate || 0,
            unit: containerType.label,
            quantity: qty,
            unitPrice: unitPrice,
            totalAmount: unitPrice * qty,
            note: item['note'],
            domesticCurrency: 'VND',
            domesticUnitPrice: 0,
            domesticTotalAmount: 0
          };
          this.sellingRates.push(addCharge)
        }
      }
    }
  }


  public processAirQuote(quote: any): void {
    this.airQuote = quote;

    let qty: number = this.inquiry['chargeableWeight'] || this.inquiry['grossWeightKg'] || 1;

    let unitPrice: number = quote.priceGroup['selectedPrice'] || 0;
    if (unitPrice > 0) {
      this.sellingRates.push({
        group: this.inquiry.mode,
        type: 'AIRFREIGHT',
        payerPartnerId: this.inquiry.clientPartnerId,
        payerPartnerLabel: this.inquiry.clientLabel,
        code: 'S_AF',
        name: 'AIRFREIGHT',
        currency: quote.currency || "USD",
        quantity: qty,
        unit: 'KGS',
        unitPrice: unitPrice,
        totalAmount: unitPrice * qty,
        note: quote['note'],
        domesticCurrency: 'VND',
        domesticUnitPrice: 0,
        domesticTotalAmount: 0
      });
    }
  }

  public buildBookingModel(): any {
    return {
      inquiry: InquiryUtil.cloneSpecificInquiry(this.inquiry, true),
      requestReference: this.inquiry['referenceCode'],
      airQuote: this.airQuote,
      seaQuote: this.seaQuote,
      bookingDate: TimeUtil.javaCompactDateTimeFormat(new Date()),
      paymentTerm: FreightTerm.PREPAID,
      shipmentInfo: this.shipmentInfo,
      shipmentType: "FREE-HAND",
      clientPartnerId: this.inquiry.clientPartnerId,
      clientLabel: this.inquiry.clientLabel,
      fromLocationCode: this.inquiry.fromLocationCode,
      fromLocationLabel: this.inquiry.fromLocationLabel,
      toLocationCode: this.inquiry.toLocationCode,
      toLocationLabel: this.inquiry.toLocationLabel,
      cargoPickupAt: this.inquiry.pickupAddress,
      cargoDeliveryAt: this.inquiry.deliveryAddress,
      unitOfPackage: this.inquiry.containerTypes,
      grossWeight: this.inquiry.grossWeightKg,
      volume: this.inquiry.volumeCbm,
      packages: this.inquiry.packageQty,
      commodity: this.inquiry.commodity,
      descriptionOfGoods: this.inquiry.descOfGoods,
      sellingRates: this.sellingRates,
    };
  }

}
