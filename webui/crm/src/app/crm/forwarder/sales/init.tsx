import React from "react";
import * as icon from 'react-feather';
import { app, bs } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { UIInquiryRequestList, UIInquiryRequestSpacePlugin } from "../price";
import { LogisticSalesProjectPlugin } from "./project/LogisticSalesProjectPlugin";
import { WCRMRateFinder } from "./quotation/request/WCRMRateFinder";
import { PartnerDashboardPage } from "./dashboard/UIPartnerWindow";
import { UIMyTransactionPage, UIMyTransactionWindow } from "./dashboard/UIMyTransactionWindow";
import { UISalesDailyTaskCalendar } from "./report/UIDailyTaskCalendarView";
import { SalesDailyTaskCalendarPlugin } from "./report/SalesDailyTaskCalendarPlugin";
import { UISaleDashboard } from "./dashboard/UISaleDashboardPage";
import { UIPricingDashboard } from "../price/request/UIPricingDashboard";

import space = app.space;

module.project.ProjectPluginManager.register(new LogisticSalesProjectPlugin());

class SaleSpacePlugin extends space.SpacePlugin {
  constructor() {
    super('forwarder/sales', 'Sale Navigation');
  }

  override createUserScreens(): space.ScreenConfig[] {
    if (bs.ScreenUtil.isMobileScreen()) return this.createUserMobileSreens();
    else return this.createUserDesktopSreens();
  }

  createUserMobileSreens(): space.ScreenConfig[] {
    return [
      {
        id: "crm", label: "CRM", icon: icon.ExternalLink,
        checkPermission: {
          feature: { module: 'logistics', name: 'user-logistics-sales' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <WCRMRateFinder appContext={appCtx} pageContext={pageCtx} />
        },
        screens: [
          {
            id: 'sale-inquiry-requests', label: 'Inquiry Requests', icon: icon.CheckSquare,
            checkPermission: {
              feature: { module: 'logistics', name: 'user-logistics-sales' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <UIInquiryRequestList appContext={appCtx} pageContext={pageCtx} space="User"
                  plugin={new UIInquiryRequestSpacePlugin('User')} />
              )
            },
          },
        ]
      }
    ]
  }

  createUserDesktopSreens(): space.ScreenConfig[] {
    return [
      {
        id: "crm", label: "CRM", icon: icon.Globe,
        checkPermission: {
          feature: { module: 'logistics', name: 'user-logistics-sales' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <WCRMRateFinder appContext={appCtx} pageContext={pageCtx} />
        },
        screens: [
          {
            id: 'user-sale-quick-rate-finder', label: 'Quick Rate Finder', icon: icon.Search,
            checkPermission: {
              feature: { module: 'logistics', name: 'user-logistics-sales' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <WCRMRateFinder appContext={appCtx} pageContext={pageCtx} />
            },
          },
          {
            id: 'user-transactions', label: 'Transactions', icon: icon.FileText,
            checkPermission: {
              feature: { module: 'logistics', name: 'user-logistics-sales' },
              requiredCapability: app.WRITE
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UIMyTransactionWindow appContext={appCtx} pageContext={pageCtx} />
            },
          },
          {
            id: 'user-crm-partners', label: 'Partners', icon: icon.UserCheck,
            checkPermission: {
              feature: { module: 'logistics', name: 'user-logistics-sales' },
              requiredCapability: app.WRITE
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <PartnerDashboardPage appContext={appCtx} pageContext={pageCtx} space="User" />
            },
          },
          {
            id: 'user-tasks-calendar', label: 'Tasks Calendar', icon: icon.Calendar,
            checkPermission: {
              feature: { module: 'logistics', name: 'user-logistics-sales' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UISalesDailyTaskCalendar appContext={appCtx} pageContext={pageCtx} space="User" />;
            },
          },
        ]
      },
    ];
  }

  override createCompanyScreens(): space.ScreenConfig[] {
    return [
      {
        id: "company-crm-dashboard", label: "CRM", icon: icon.Activity,
        checkPermission: {
          feature: { module: 'logistics', name: 'user-logistics-sales' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <UISaleDashboard appContext={appCtx} pageContext={pageCtx} space="Company" />;
        },
        screens: [
          {
            id: 'company-pricing-dashboard', label: 'Pricing Dashboard', icon: icon.Trello,
            checkPermission: {
              feature: { module: 'logistics', name: 'user-logistics-sales' },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <UIPricingDashboard appContext={appCtx} pageContext={pageCtx} space="Company" />
              )
            },
          },
          {
            id: 'company-crm-partners', label: 'Partners', icon: icon.UserCheck,
            checkPermission: {
              feature: { module: 'logistics', name: 'user-logistics-sales' },
              requiredCapability: app.READ
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <PartnerDashboardPage appContext={appCtx} pageContext={pageCtx} space="Company" />
            },
          },
          {
            id: 'company-tasks-calendar', label: 'Tasks Calendar', icon: icon.Calendar,
            checkPermission: {
              feature: { module: 'logistics', name: 'user-logistics-sales' },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UISalesDailyTaskCalendar appContext={appCtx} pageContext={pageCtx} space="Company" />;
            },
          },
        ]
      }
    ]
  }

  override createSystemScreens(): app.space.ScreenConfig[] {
    const space = 'System';

    return [
      {
        id: "company-crm", label: "CRM", icon: icon.Activity,
        checkPermission: {
          feature: { module: 'logistics', name: 'user-logistics-sales' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <UISaleDashboard appContext={appCtx} pageContext={pageCtx} space={space} />;
        },
        screens: [
          {
            id: 'system-crm-partners', label: 'Partners', icon: icon.UserCheck,
            checkPermission: {
              feature: { module: 'logistics', name: 'user-logistics-sales' },
              requiredCapability: app.READ
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <PartnerDashboardPage appContext={appCtx} pageContext={pageCtx} space="System" />
            },
          },
          {
            id: 'company-pricing-dashboard', label: 'Pricing Dashboard', icon: icon.Trello,
            checkPermission: {
              feature: { module: 'logistics', name: 'user-logistics-sales' },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <UIPricingDashboard appContext={appCtx} pageContext={pageCtx} space="System" />
              )
            },
          },
          {
            id: 'system-tasks-calendar', label: 'Tasks Calendar', icon: icon.Calendar,
            checkPermission: {
              feature: { module: 'logistics', name: 'user-logistics-sales' },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UISalesDailyTaskCalendar appContext={appCtx} pageContext={pageCtx} space="System" />;
            },
          },
        ]
      }
    ]
  }

}

export function init() {
  space.SpacePluginManager.register(new SaleSpacePlugin());
}

module.common.DbEntityTaskCalendarPluginManger.register(new SalesDailyTaskCalendarPlugin());

