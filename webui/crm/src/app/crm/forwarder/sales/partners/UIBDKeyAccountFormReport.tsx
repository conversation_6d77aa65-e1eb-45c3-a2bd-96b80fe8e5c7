
import React from 'react';
import * as FeatherIcon from 'react-feather';

import { bs, app, input, server, util, entity } from '@datatp-ui/lib';
import {
  UISalemanKeyAccountReportList,
  UISalemanKeyAccountReportPlugin
} from './UISalemanKeyAccountReportList';
import {
  UIVolumeSalemanKeyAccountReportList,
  UIVolumeSalemanKeyAccountReportPlugin
} from './UIVolumeSalemanKeyAccountReportList';
import { WKeyAccountReportTypeSelector, WQuickTimeRangeSelector } from '../dashboard/UIDashboardUtility';
import { GridConfig, ResponsiveGrid } from '../dashboard/ResponsiveGrid';
import { UIKeyAccountPerformance } from '../dashboard/UIKeyAccountPerformance';

const SESSION = app.host.DATATP_HOST.session;

export class ReportBean {
  id: number | undefined;
  type: 'BD' | 'SALES';
  code: string;
  suggestionOrRequest: string;
  salemanAccountId: number;
  salemanLabel: string;
  reportedDateFrom: string;
  reportedDateTo: string;
  volumePerformance: any;
  highlights: {
    signedAaContracts: string,
    meetingIn2Weeks: string,
    newAgentsApproachedIn2Weeks: string,
    otherHighlights: string,
    lowlights: string
  };
  forecast: {
    airVolume: string,
    seaVolume: string,
    estimate: string
  };
}

export interface UIBDKeyAccountFormReportProps extends app.AppComponentProps {
  initReportBean?: ReportBean
}
export class UIBDKeyAccountFormReport extends app.AppComponent<UIBDKeyAccountFormReportProps> {
  reportBean: ReportBean;
  viewId: number = util.IDTracker.next();

  constructor(props: UIBDKeyAccountFormReportProps) {
    super(props);
    const { initReportBean } = this.props;
    if (initReportBean) {
      this.reportBean = initReportBean;
    } else {
      this.reportBean = this.initialReportBean();
    }
  }

  initialReportBean(): ReportBean {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    firstDayOfMonth.setHours(0, 0, 0, 0);
    lastDayOfMonth.setHours(23, 59, 59, 999);

    let reportBean: ReportBean = {
      id: undefined,
      type: 'SALES',
      reportedDateFrom: util.TimeUtil.javaCompactDateTimeFormat(firstDayOfMonth),
      reportedDateTo: util.TimeUtil.javaCompactDateTimeFormat(lastDayOfMonth),
      salemanAccountId: SESSION.getAccountId(),
      salemanLabel: SESSION.getAccountAcl().getFullName(),
      code: '',
      suggestionOrRequest: '',
      volumePerformance: {},
      highlights: {
        signedAaContracts: '',
        meetingIn2Weeks: '',
        newAgentsApproachedIn2Weeks: '',
        otherHighlights: '',
        lowlights: ''
      },
      forecast: {
        airVolume: '',
        seaVolume: '',
        estimate: ''
      }
    }
    return reportBean;
  }

  onSave = () => {
    let { appContext } = this.props;
    appContext.createHttpBackendCall('PartnerReportService', 'saveSalemanKeyAccountReport', { report: this.reportBean })
      .withEntityOpNotification('commit', 'Sale Account Report')
      .withSuccessData((reportBeanInDb: any) => {
        this.reportBean = reportBeanInDb
        this.viewId = util.IDTracker.next();
        this.forceUpdate();
      })
      .call();
  }

  onViewAllReports = () => {
    let { pageContext } = this.props;

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      const onSelect = (appCtx: app.AppContext, pageCtx: app.PageContext, entity: any) => {

        let volumePerformance: any = typeof entity['volumePerformance'] === 'string'
          ? JSON.parse(entity['volumePerformance'])
          : (entity['volumePerformance'] || {});
        entity['volumePerformance'] = volumePerformance;

        let forecast: any = typeof entity['forecast'] === 'string'
          ? JSON.parse(entity['forecast'])
          : (entity['forecast'] || {});
        entity['forecast'] = forecast;

        let highlights: any = typeof entity['highlights'] === 'string'
          ? JSON.parse(entity['highlights'])
          : (entity['highlights'] || {});
        entity['highlights'] = highlights;

        pageCtx.back();
        this.reportBean = entity;
        this.forceUpdate();
      }

      return (
        <UISalemanKeyAccountReportList
          appContext={appCtx} pageContext={pageCtx} plugin={new UISalemanKeyAccountReportPlugin('User')}
          onSelect={onSelect} />
      )
    }
    pageContext.createPopupPage('saleman-account-report', "Saleman Key Account Report", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewAllPerformance = () => {
    let { pageContext } = this.props;
    const { reportedDateFrom, reportedDateTo } = this.reportBean;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVolumeSalemanKeyAccountReportList
          appContext={appCtx} pageContext={pageCtx}
          plugin={new UIVolumeSalemanKeyAccountReportPlugin().withReportedDate(reportedDateFrom, reportedDateTo)} />
      )
    }
    pageContext.createPopupPage('performance', "Performance", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onModifyHighlights = (bean: any | Array<any>, _field: string, _oldVal: any, newVal: any) => {
    let highlights: any = this.reportBean['highlights'] || {}
    this.reportBean['highlights'] = highlights;
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  onModifyForecast = (bean: any | Array<any>, _field: string, _oldVal: any, newVal: any) => {
    let forecast: any = this.reportBean['forecast'] || {}
    this.reportBean['forecast'] = forecast;
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  render() {

    const { appContext, pageContext } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    const highlights: any = this.reportBean['highlights'] || {}
    const forecast: any = this.reportBean['forecast'] || {}

    const isOwnerReport: boolean = this.reportBean['salemanAccountId'] === SESSION.getAccountId();

    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    return (
      <div className="flex-vbox bg-white rounded-md" >
        <div className="flex-vbox bg-white rounded-md bg-body-highlight">
          <bs.GreedyScrollable className="my-1">

            {/* ------------------- Key Account Performance ----------------------- */}
            <div className="flex-vbox flex-grow-0">
              <UIKeyAccountPerformance appContext={appContext} pageContext={pageContext} space={'User'} hiddenHeader />
            </div>

            {/* ------------------- Highlights ----------------------- */}
            <div className="flex-hbox align-items-start justify-content-between flex-grow-0 flex-shrink-0 p-1 mb-1 gap-1" key={this.viewId}>

              <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
                style={{
                  borderColor: borderColor,
                  transition: 'all 0.3s ease',
                  marginBottom: '10px'
                }}
                onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>

                <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
                    <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.Award className="me-2" size={18} />HIGHLIGHTS</h5>
                  </div>

                  <div className="flex-vbox">
                    <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                      <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                        <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                          <FeatherIcon.FileText className="me-2 text-primary" size={14} />
                          <span>Signed AA/Contracts</span>
                        </div>
                        <input.BBTextField bean={highlights} field="signedAaContracts" style={{ height: '4em' }}
                          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-header mb-2">
                          <span className="tooltip-title">Signed AA/Contracts:</span>
                        </div>
                        <ul className="mb-2 ps-3">
                          {(highlights['signedAaContracts'] || '...').split('\n').map((line: string, i: number) => (
                            <li key={i}>{line}</li>
                          ))}
                        </ul>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>

                    <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                      <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                        <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                          <FeatherIcon.Calendar className="me-2 text-info" size={14} />
                          <span>Meeting in 2 weeks</span>
                        </div>
                        <input.BBTextField bean={highlights} field="meetingIn2Weeks" style={{ height: '4em' }}
                          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-header mb-2">
                          <span className="tooltip-title">Meeting in 2 weeks:</span>
                        </div>
                        <ul className="mb-2 ps-3">
                          {(highlights['meetingIn2Weeks'] || '...').split('\n').map((line: string, i: number) => (
                            <li key={i}>{line}</li>
                          ))}
                        </ul>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>

                    <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                      <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                        <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                          <FeatherIcon.Users className="me-2 text-success" size={14} />
                          <span>New Agents approached in 2 weeks</span>
                        </div>
                        <input.BBTextField bean={highlights} field="newAgentsApproachedIn2Weeks" style={{ height: '4em' }}
                          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-header mb-2">
                          <span className="tooltip-title">New Agents approached in 2 weeks:</span>
                        </div>
                        <ul className="mb-2 ps-3">
                          {(highlights['newAgentsApproachedIn2Weeks'] || '...').split('\n').map((line: string, i: number) => (
                            <li key={i}>{line}</li>
                          ))}
                        </ul>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>

                    <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                      <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                        <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                          <FeatherIcon.Star className="me-2 text-warning" size={14} />
                          <span>Other highlights</span>
                        </div>
                        <input.BBTextField bean={highlights} field="otherHighlights" style={{ height: '4em' }}
                          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-header mb-2">
                          <span className="tooltip-title">Other highlights:</span>
                        </div>
                        <ul className="mb-2 ps-3">
                          {(highlights['otherHighlights'] || '...').split('\n').map((line: string, i: number) => (
                            <li key={i}>{line}</li>
                          ))}
                        </ul>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>

                    <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                      <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                        <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                          <h6 className="fw-bold py-2"><FeatherIcon.AlertTriangle className="me-2" size={16} />LOWLIGHTS</h6>
                        </div>
                        <input.BBTextField bean={highlights} field="lowlights" style={{ height: '4em' }}
                          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-header mb-2">
                          <span className="tooltip-title">LOWLIGHTS:</span>
                        </div>
                        <ul className="mb-2 ps-3">
                          {(highlights['lowlights'] || '...').split('\n').map((line: string, i: number) => (
                            <li key={i}>{line}</li>
                          ))}
                        </ul>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>
                  </div>
                </div>
              </div>

              {/* ------------------- Forecast | Suggestion/ Request ----------------------- */}
              <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center gap-1"
                style={{
                  borderColor: borderColor,
                  transition: 'all 0.3s ease',
                  marginBottom: '10px'
                }}
                onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>

                <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100" style={{ minHeight: 250 }}>
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
                    <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.Star className="me-2" size={18} />FORECAST</h5>
                  </div>

                  <div className="flex-vbox">
                    <div className="flex-hbox justify-content-start align-items-center px-0 py-1">
                      <div className='flex-hbox justify-content-start align-items-center flex-grow-0 me-2' style={{ minWidth: 130 }}>
                        <FeatherIcon.Star className="me-2 text-warning" size={12} />
                        <span className="text-nowrap">Air Volume</span>
                      </div>
                      <input.BBStringField bean={forecast} field="airVolume" />
                    </div>

                    <div className="flex-hbox justify-content-start align-items-center px-0 py-1">
                      <div className='flex-hbox justify-content-start align-items-center flex-grow-0 me-2' style={{ minWidth: 130 }}>
                        <FeatherIcon.Star className="me-2 text-warning" size={12} />
                        <span className="text-nowrap">Sea Volume</span>
                      </div>

                      <input.BBStringField bean={forecast} field="seaVolume" />
                    </div>

                    <div className="flex-hbox justify-content-start align-items-center px-0 py-1">
                      <div className='flex-hbox justify-content-start align-items-center flex-grow-0 me-2' style={{ minWidth: 130 }}>
                        <FeatherIcon.Star className="me-2 text-warning" size={12} />
                        <span className='text-nowrap'>Estimate</span>
                      </div>
                      <input.BBStringField bean={forecast} field="estimate" />
                    </div>
                  </div>

                </div>

                <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100 mt-2">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
                    <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.MessageCircle className="me-2" size={18} />SUGGESTION/ REQUEST</h5>
                  </div>

                  <div className="flex-vbox p-2">
                    <input.BBTextField bean={this.reportBean} field="suggestionOrRequest" style={{
                      height: 170,
                      fontSize: '1rem',
                    }}
                      placeholder='Any suggestion/ request for improvement?' />
                  </div>
                </div>
              </div>
            </div>
          </bs.GreedyScrollable>
        </div>

        <bs.Toolbar className='border' hide={!writeCap || !isOwnerReport}>
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext}
            label={'Save'} onClick={this.onSave} />
        </bs.Toolbar>
      </div>
    )
  }
}
