import React from "react";

import * as FeatherIcon from 'react-feather'
import { util, grid, bs, entity, input, sql, app } from '@datatp-ui/lib';
import { T } from "../backend";
import { UISalemanKeyAccountFormReport } from "./UISalemanKeyAccountReport";
import { UIBDKeyAccountFormReport } from "./UIBDKeyAccountFormReport";

const SESSION = app.host.DATATP_HOST.session;

export class UISalemanKeyAccountReportPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      service: 'PartnerReportService',
      searchMethod: 'searchSalemanKeyAccountReports',
      deleteMethod: 'deleteSalemanKeyAccountReports',
    }
    this.searchParams = {
      params: {
        space: space,
      },
      filters: [...sql.createSearchFilter()],
      maxReturn: 1000
    }
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

  withSalemanAccountId(salemanAccountId: number) {
    this.addSearchParam("salemanAccountId", salemanAccountId)
    return this;
  }

}

export class UISalemanKeyAccountReportList extends entity.DbEntityList {

  createVGridConfig() {
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('code', T('Code'), 200, 'fixed-left'),
          {
            name: 'submittedDate', label: T('Submitted Date'), width: 130,
            format: util.text.formater.compactDate
          },
          { name: 'salemanLabel', label: T('Salesman'), width: 250, },
          {
            name: 'reportedDateFrom', label: T('Report From'), width: 130,
            format: util.text.formater.compactDate
          },
          {
            name: 'reportedDateTo', label: T('Report To'), width: 130,
            format: util.text.formater.compactDate
          },
          { name: 'revenue', label: T('Revenue'), width: 150 },
          { name: 'profit', label: T('Profit'), width: 150 },
          { name: 'suggestionOrRequest', label: T('Suggestions/Requests'), width: 350 },
        ],
      },
      toolbar: {
        hide: true
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    const { pageContext, appContext } = this.props;
    let record: any = dRecord.record || {};
    let reportId: number = record['id'];
    appContext.createHttpBackendCall("PartnerReportService", "getSalemanKeyAccountReportById", { id: reportId })
      .withSuccessData((report: any) => {

        let volumePerformance: any = typeof report['volumePerformance'] === 'string'
          ? JSON.parse(report['volumePerformance'])
          : (report['volumePerformance'] || {});
        report['volumePerformance'] = volumePerformance;

        let forecast: any = typeof report['forecast'] === 'string'
          ? JSON.parse(report['forecast'])
          : (report['forecast'] || {});
        report['forecast'] = forecast;

        let highlights: any = typeof report['highlights'] === 'string'
          ? JSON.parse(report['highlights'])
          : (report['highlights'] || {});
        report['highlights'] = highlights;


        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <UISalemanKeyAccountFormReport appContext={appCtx} pageContext={pageCtx} initReportBean={report} />
        }

        if (report['reportType'] === 'BD') {
          createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return <UIBDKeyAccountFormReport appContext={appCtx} pageContext={pageCtx} initReportBean={report} />
          }
        }

        let popupId = `make-key-account-report-${util.IDTracker.next()}`;
        let popupLabel: string = `Key Account Report - ${report['salemanLabel'] || 'N/A'}`;
        pageContext.createPopupPage(popupId, popupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
      })
      .call()
  }

  onDeleteAction(): void {
    const { appContext, plugin } = this.props;
    const selectedIds = plugin.getListModel().getSelectedRecordIds();

    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("warning"), T("No saleman account report selected!"));
      return;
    }

    const callbackConfirm = () => {
      appContext.createHttpBackendCall('PartnerReportService', 'deleteSalemanKeyAccountReports', { targetIds: selectedIds })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T("Delete saleman account report success"));
          this.reloadData();
        })
        .call();
    }
    let message = (<div className="text-danger">Do you want to delete these partner report?</div>);
    bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    plugin.searchParams = searchParam;
    this.reloadData();
    this.nextViewId();
    this.vgridContext.getVGrid().forceUpdateView();
  };

  render() {
    if (this.isLoading()) return this.renderLoading();
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1 mt-1'>

          <div className='flex-hbox justify-content-start align-items-center'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={searchParam} field={"maxReturn"}
                options={[1000, 2000, 5000, 10000]}
                optionLabels={['Show 1000 records', 'Show 2000 records', 'Show 5000 records', 'Show 10.000 records']}
                onInputChange={this.onModify} />
            </div>
          </div>

          <div className="flex-hbox justify-content-end align-items-center flex-grow-1">
            <bs.Button laf='warning' className="border-0 p-1 border-end rounded-0" outline
              onClick={() => this.onDeleteAction()}>
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button>
          </div>

        </div>
        <div key={this.viewId} className='flex-vbox'>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }
}

