import React from "react";

import * as FeatherIcon from 'react-feather';
import { bs, app, util, entity } from '@datatp-ui/lib';
import { UIKeyAccountPerformance } from "./UIKeyAccountPerformance";

export interface UIPartnerDashboardProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
export class UIPartnerDashboard extends app.AppComponent<UIPartnerDashboardProps> {

  renderHeader() {
    return (
      <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
        <h5 style={{ color: '#6c757d' }}>
          <FeatherIcon.BarChart2 className="me-2" size={18} />
          Partner Dashboard Overview
        </h5>

        <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

        </div>

      </div>

    )
  }

  render(): React.ReactNode {
    const { appContext, pageContext } = this.props;
    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };


    return (
      <div className="flex-vbox p-1 bg-white rounded-md w-100 h-100 my-2" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight" key={util.IDTracker.next()}>

          <div className="flex-vbox flex-grow-0 mt-1 mx-1">
            <UIKeyAccountPerformance appContext={appContext} pageContext={pageContext} space={'User'} />
          </div>

          <div className="flex-vbox">

          </div>

        </div>

      </div>
    )
  }

}