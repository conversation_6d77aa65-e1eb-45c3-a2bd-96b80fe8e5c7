import React from "react";
import { bs } from "@datatp-ui/lib";
import { module } from "@datatp-ui/erp";
import { app as lgc_app } from '@datatp-ui/logistics';

import { T } from "../backend";
import {
  toConvertCurrency, AdditionalChargeCalculator,
  CommissionDistributionCalculator,
  toDomesticCharge, toCustomerCharge,
  applyMarkup
} from "./utilities";
import { ICalculatorContext } from "./CalculatorContext";
import { ContainerType } from "../../common/ContainerTypeUtil";

import _settings = lgc_app.logistics.settings;
import CustomClearanceType = _settings.CustomClearanceType;
import TruckType = _settings.TruckType;
import SeaType = _settings.SeaType;
import VolumeUnit = module.settings.unit.VolumeUnit;

class CustomerTransportChargeCalculator {

  calculateDomesticCharge(ctx: ICalculatorContext, price: any) {
    const exchangeRateDB = ctx.exchangeRateDB;
    if (!exchangeRateDB) return;
    const domesticCurrency = ctx.chargeMargin.defaultCurrency;
    const currency = price.currency;
    price.domesticCurrency = domesticCurrency;

    price.shipmentDomesticTotalCharge = toDomesticCharge(
      exchangeRateDB, price.shipmentTotalCharge, currency, ctx.chargeMargin);

    price.shipmentDomesticTotalTax = toDomesticCharge(
      exchangeRateDB, price.shipmentTotalTax, currency, ctx.chargeMargin);

    price.additionalDomesticTotalCharge =
      toDomesticCharge(exchangeRateDB, price.additionalTotalCharge, currency, ctx.chargeMargin);
    price.additionalDomesticTotalTax =
      toDomesticCharge(exchangeRateDB, price.additionalTotalTaxCharge, currency, ctx.chargeMargin);
    price.domesticFinalCommission =
      toDomesticCharge(exchangeRateDB, price.finalCommission, currency, ctx.chargeMargin);
    price.domesticFinalCharge = toDomesticCharge(exchangeRateDB, price.finalCharge, currency, ctx.chargeMargin);
  }

  calculateAdditionalCharges(ctx: ICalculatorContext, charge: any, addCharges: Array<any>) {
    let converter = ctx.exchangeRateDB;
    if (!converter) return;
    let totalAdditionalCharge = 0;
    let totalTaxAdditionalCharge = 0;
    if (addCharges?.length) {
      for (let addCharge of addCharges) {
        if (!addCharge) continue;
        if (!addCharge.refCurrency) addCharge.refCurrency = charge.refCurrency;
        if (!addCharge.currency) addCharge.currency = charge.currency;
        new AdditionalChargeCalculator().calculate(ctx, addCharge);
        let currency = addCharge.currency;
        let transportChargeCurrency = charge.currency;
        totalAdditionalCharge += toConvertCurrency(converter, addCharge.total, currency, transportChargeCurrency);
        totalTaxAdditionalCharge += toConvertCurrency(converter, addCharge.totalTax, currency, transportChargeCurrency);
      }
    };
    charge.additionalTotalCharge = totalAdditionalCharge;
    charge.additionalTotalTaxCharge = totalTaxAdditionalCharge;
  }

  calculateCommissions(ctx: ICalculatorContext, price: any, commissions: Array<any>) {
    if (!commissions) return;
    if (!ctx.exchangeRateDB) return;
    let totalCommission = 0;
    if (commissions?.length) {
      for (const commission of commissions) {
        calculator.commission.calculateCommission(ctx, price, commission);
        totalCommission += toConvertCurrency(ctx.exchangeRateDB, commission.total, commission.currency, price.currency);
      }
    }
    price.totalCommission = totalCommission;
  }

  computeSelectedPrice(ctx: ICalculatorContext, charge: any, marginConfig: any) {
    const converter = ctx.exchangeRateDB;
    if (!converter) return;
    let selectedPriceType = charge.selectedPriceType;
    let price = charge[selectedPriceType];
    if (price !== 0) return price;
    let refPriceField = `ref${selectedPriceType.charAt(0).toUpperCase() + selectedPriceType.slice(1)}`;
    return toCustomerCharge(converter, charge[refPriceField], charge.refCurrency, charge.currency, marginConfig);
  }

  calculateContainerPrice(charge: any, amountPropertyName: string, costPropertyName: string) {
    let cost = 0;
    if (charge[costPropertyName] && charge[amountPropertyName]) {
      cost = charge[costPropertyName] * charge[amountPropertyName]
    }
    return cost;
  }

  calculateFinalCharge(charge: any) {
    if (!charge.shipmentTotalTax) charge.shipmentTotalTax = 0;
    if (!charge.shipmentTotalCharge) charge.shipmentTotalCharge = 0;
    if (!charge.additionalTotalCharge) charge.additionalTotalCharge = 0;
    if (!charge.additionalTotalTaxCharge) charge.additionalTotalTaxCharge = 0;
    if (!charge.finalCommission) charge.finalCommission = 0;
    charge.finalCharge = charge.shipmentTotalTax + charge.shipmentTotalCharge +
      charge.additionalTotalCharge + charge.additionalTotalTaxCharge + charge.finalCommission;
  }
}

// ==========================================================
// AIR PRICE
// ==========================================================
class AirQuoteCalculator extends CustomerTransportChargeCalculator {

  calculatorQuote(quote: any, chargeableWeight: number, resetSelling: boolean = false) {
    if (!quote) return;
    const priceGroup: any = quote.priceGroup || {};
    const selectedPriceType = this.computePriceType(chargeableWeight);
    const refSelectedPriceType: string = `ref${selectedPriceType.charAt(0).toUpperCase() + selectedPriceType.slice(1)}`
    priceGroup["selectedPriceType"] = selectedPriceType;
    if (resetSelling) {
      priceGroup.selectedPrice = priceGroup[refSelectedPriceType];
    }
    quote['priceGroup'] = priceGroup;
  }

  applyMargin(ctx: ICalculatorContext, quote: any, force: boolean = false) {
    const priceGroup: any = quote.priceGroup || {};
    let priceValue: number = priceGroup['selectedPrice'] || 0;

    if (priceValue === 0 || force) {
      const converter = ctx.exchangeRateDB;
      if (!converter) return;
      const margin: any = ctx.chargeMargin?.airTransportChargeMargin;

      const chargeableWeight = quote.chargeableWeight || 0;
      const selectedPriceType = this.computePriceType(chargeableWeight);
      priceGroup["selectedPriceType"] = selectedPriceType;
      const refSelectedPriceType: string = `ref${selectedPriceType.charAt(0).toUpperCase() + selectedPriceType.slice(1)}`
      const costing: number = priceGroup[refSelectedPriceType] || 0;
      priceValue = toCustomerCharge(converter, costing, quote.refCurrency, quote.currency, margin);
      priceGroup['selectedPrice'] = priceValue;
      quote['priceGroup'] = priceGroup;
    }
  }

  calculate(ctx: ICalculatorContext, customerAirCharge: any) {
    if (!customerAirCharge) return;
    try {
      const grossWeightInKG = ctx.grossWeightInKG;
      let volumeInCM3 = VolumeUnit.convert(ctx.volumeInCbm, VolumeUnit.M3.unit, VolumeUnit.CM3.unit);
      let chargeableWeight = this.calculateChargeableWeight(ctx.chargeableWeight, grossWeightInKG, volumeInCM3);
      volumeInCM3 = this.calculateChargeableVolume(ctx.chargeableWeight, grossWeightInKG, volumeInCM3);
      customerAirCharge.chargeableWeightInKG = chargeableWeight;
      customerAirCharge.chargeableVolumeInCBM = volumeInCM3;

      this.calculateTotalCharge(ctx, customerAirCharge);
      this.calculateAdditionalCharges(ctx, customerAirCharge, customerAirCharge.additionalCharges);
      this.calculateCommissions(ctx, customerAirCharge, customerAirCharge.commissionDistributions);
      this.calculateFinalCharge(customerAirCharge);
      this.calculateDomesticCharge(ctx, customerAirCharge);
    } catch (err) {
      alert(`Can't calculate for Air Transport Charge : \n${err}`);
    }
  }

  calculateChargeableWeight(chargeableWeight: number, grossWeight: number, volumeInCM3: number) {
    if (chargeableWeight > 0) return chargeableWeight;
    let volumeWeight = volumeInCM3;
    return Math.max(volumeWeight, grossWeight);
  }

  calculateChargeableVolume(chargeableWeight: number, grossWeight: number, volumeInCM3: number) {
    if (chargeableWeight > 0) grossWeight = chargeableWeight;
    let chargeableVolume = grossWeight;
    return Math.max(chargeableVolume, volumeInCM3);
  }

  calculateTotalCharge(ctx: ICalculatorContext, charge: any) {
    const chargeableWeight: number = charge.chargeableWeightInKG;
    if (!charge.selectedPriceType) {
      charge.selectedPriceType = this.computePriceType(chargeableWeight);
    }
    charge.selectedPrice = charge[charge.selectedPriceType];

    if (charge.selectedPriceType === 'minPrice') {
      charge.shipmentTotalCharge = charge.minPrice;
    } else {
      charge.shipmentTotalCharge = chargeableWeight * charge[charge.selectedPriceType];
    }

    charge.shipmentTotalTax = charge.shipmentTotalCharge * charge.taxRate;
  }

  computePriceType(chargeableWeight: number) {
    let priceType;
    if (chargeableWeight <= 10) priceType = "minPrice";
    else if (chargeableWeight < 45) priceType = "level1Price";
    else if (chargeableWeight < 100) priceType = "level2Price";
    else if (chargeableWeight < 300) priceType = "level3Price";
    else if (chargeableWeight < 500) priceType = "level4Price";
    else if (chargeableWeight < 1000) priceType = "level5Price";
    else priceType = "level6Price";
    return priceType;
  }

}

// ==========================================================
// FCL PRICE
// ==========================================================
class CustomerSeaFCLTransportChargeCalculator extends CustomerTransportChargeCalculator {

  calculatorQuote(ctx: ICalculatorContext, quote: any) {
    if (!quote) return;
    let containers: ContainerType[] = ctx.typeOfContainers || [];
    for (let container of containers) {
      let fieldName: string | undefined = container.toFCLPriceLevel();
      if (fieldName) {
        //TODO: Dan - Implement this code
      }
    }

    const priceGroup: any = quote.priceGroup || {};
    quote['priceGroup'] = priceGroup;
  }

  resetQuoteCosting(ctx: ICalculatorContext, quote: any) {
    if (!quote) return;
    let containers: ContainerType[] = ctx.typeOfContainers || [];
    const priceGroup: any = quote.priceGroup || {};
    for (let container of containers) {
      let fieldName: string | undefined = container.toFCLPriceLevel();
      if (fieldName) {
        const refFieldName: string = `ref${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}`
        let originCost: number = priceGroup[refFieldName] || 0;
        priceGroup[fieldName] = originCost;
      }
    }
    quote['priceGroup'] = priceGroup;
  }

  applyMargin(ctx: ICalculatorContext, quote: any, _force: boolean = false) {
    const { typeOfContainers, chargeMargin } = ctx;
    const priceGroup: any = quote.priceGroup || {};
    let containers: ContainerType[] = typeOfContainers || [];
    const margin: any = chargeMargin?.seaTransportChargeMargin;
    if (!margin) {
      let message = (<div className="ms-1 text-warning py-3 border-bottom">Missing margin config!!!</div>)
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'sm' });
    }
    for (let container of containers) {
      let fieldName: string | undefined = container.toFCLPriceLevel();
      if (fieldName) {
        let priceValue: number = priceGroup[fieldName] || 0;
        if (priceValue !== 0) {
          priceValue = applyMarkup(priceValue, margin);
          priceGroup[fieldName] = priceValue;
          quote['priceGroup'] = priceGroup;
        }
      }
    }
  }

  calculate(ctx: ICalculatorContext, customerFCLPrice: any) {
    if (!customerFCLPrice) return;
    if (SeaType.FCL != customerFCLPrice.type) {
      bs.notificationShow("danger", T("Expecting Transport Mode: FCL"));
      return;
    };
    this.calculateAdditionalCharges(ctx, customerFCLPrice, customerFCLPrice.additionalCharges);
    this.calculateCommissions(ctx, customerFCLPrice, customerFCLPrice.commissionDistributions);
    this.calculateFinalCharge(customerFCLPrice);
    this.calculateDomesticCharge(ctx, customerFCLPrice);
  }


}

// ------------------------------ LCL PRICE ----------------------------------
class SeaLCLQuoteCalculator extends CustomerTransportChargeCalculator {

  resetQuoteCosting(ctx: ICalculatorContext, quote: any) {
    if (!quote) return;
    // if (SeaType.LCL != quote.type) {
    //   bs.notificationShow("danger", T("Expecting Type: LCL"));
    //   return;
    // }

    let containers: ContainerType[] = ctx.typeOfContainers || [];
    const priceGroup: any = quote.priceGroup || {};

    for (let container of containers) {
      let fieldName: string | undefined = container.toFCLPriceLevel();
      if (fieldName) {
        const refFieldName: string = `ref${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}`
        let originCost: number = priceGroup[refFieldName] || 0;
        priceGroup[fieldName] = originCost;
      }
    }

    quote['priceGroup'] = priceGroup;
  }

  calculatorQuote(quote: any, cbm: number, resetSelling: boolean = false) {
    if (!quote) return;
    // if (SeaType.LCL != quote.type) {
    //   bs.notificationShow("danger", T("Expecting Type: LCL"));
    //   return;
    // }
    const priceGroup: any = quote.priceGroup || {};
    const selectedPriceType = this.computePriceType(cbm);
    const refSelectedPriceType: string = `ref${selectedPriceType.charAt(0).toUpperCase() + selectedPriceType.slice(1)}`
    priceGroup["selectedPriceType"] = selectedPriceType;
    if (resetSelling) {
      priceGroup.selectedPrice = priceGroup[refSelectedPriceType];
    }
    quote['priceGroup'] = priceGroup;
  }

  applyMarginDep(ctx: ICalculatorContext, quote: any, force: boolean = false) {
    const priceGroup: any = quote.priceGroup || {};
    let priceValue: number = priceGroup['selectedPrice'] || 0;
    if (priceValue !== 0 || force) {
      const converter = ctx.exchangeRateDB;
      if (!converter) return;
      const margin: any = ctx.chargeMargin?.seaTransportChargeMargin;

      const volumeInCbm = quote.volumeInCbm || 0;
      const selectedPriceType = this.computePriceType(volumeInCbm);
      priceGroup["selectedPriceType"] = selectedPriceType;
      const refSelectedPriceType: string = `ref${selectedPriceType.charAt(0).toUpperCase() + selectedPriceType.slice(1)}`
      priceValue = toCustomerCharge(converter, priceGroup[refSelectedPriceType], quote.refCurrency, quote.currency, margin);
      priceGroup['selectedPrice'] = priceValue;
      quote['priceGroup'] = priceGroup;
    }
  }

  applyMargin(ctx: ICalculatorContext, quote: any) {
    const priceGroup: any = quote.priceGroup || {};
    let priceValue: number = priceGroup['selectedPrice'] || 0;
    if (priceValue !== 0) {
      const margin: any = ctx.chargeMargin?.seaTransportChargeMargin;
      priceValue = applyMarkup(priceValue, margin);
      priceGroup['selectedPrice'] = priceValue;
      quote['priceGroup'] = priceGroup;
    }
    return priceValue;
  }

  calculate(ctx: ICalculatorContext, seaCharge: any) {
    if (!seaCharge) return;
    // if (SeaType.LCL != seaCharge.type) {
    //   bs.notificationShow("danger", T("Expecting Type: LCL"));
    //   return;
    // }

    let grossWeight = ctx.grossWeightInKG;
    let chargeableWeight = ctx.grossWeightInKG;
    let volumeInCbm = ctx.volumeInCbm;
    let chargeableVolume = ctx.chargeableVolume;

    let chargeableWeightInKG = this.calculateChargeableWeight(chargeableWeight, grossWeight, volumeInCbm);
    let chargeableVolumeInCBM = this.calculateChargeableVolume(chargeableWeight, grossWeight, volumeInCbm, chargeableVolume);

    seaCharge.chargeableWeightInKG = chargeableWeightInKG;
    seaCharge.chargeableVolumeInCBM = chargeableVolumeInCBM;

    this.calculateAdditionalCharges(ctx, seaCharge, seaCharge.additionalCharges);
    this.calculateCommissions(ctx, seaCharge, seaCharge.commissionDistributions);
    this.calculateTotalCharge(ctx, seaCharge);
    this.calculateFinalCharge(seaCharge);
    this.calculateDomesticCharge(ctx, seaCharge);
  }

  calculateTotalCharge(ctx: ICalculatorContext, price: any) {
    const volumeInCbm: number = price.chargeableVolumeInCBM;
    let selectedPriceType: string = price.selectedPriceType;

    if (!selectedPriceType || selectedPriceType.length === 0) {
      selectedPriceType = this.computePriceType(volumeInCbm);
      price["selectedPriceType"] = selectedPriceType;
      price["selectedPrice"] = this.computeSelectedPrice(ctx, price, ctx.chargeMargin.seaTransportChargeMargin);
    } else {
      price.selectedPrice = price[selectedPriceType];
      if (selectedPriceType === "minimumChargeLCL") {
        price.shipmentTotalCharge = price.minimumChargeLCL;
      } else {
        price.shipmentTotalCharge = volumeInCbm * price.freightChargeLCL;
      }
      price.shipmentTotalTax = price.shipmentTotalCharge * price.taxRate;
    }
  }

  calculateChargeableWeight(chargeableWeight: number, grossWeight: number, volumeInCbm: number) {
    if (chargeableWeight > 0) return chargeableWeight;
    let volumeWeight = volumeInCbm;
    return Math.max(volumeWeight, grossWeight);
  }

  calculateChargeableVolume(chargeableWeight: number, grossWeight: number, volumeInCbm: number, chargeableVolume: number) {
    if (chargeableVolume > 0) return chargeableVolume;
    if (chargeableWeight > 0) grossWeight = chargeableWeight;
    let chargeableVolumeInCBM = grossWeight;
    return Math.max(chargeableVolumeInCBM, volumeInCbm);
  }

  computePriceType(volumeInCBM: number): string {
    if (volumeInCBM >= 0 && volumeInCBM < 1) return "minimumChargeLCL";
    if (volumeInCBM < 2) return "less2CbmPrice";
    if (volumeInCBM < 3) return "less3CbmPrice";
    if (volumeInCBM < 5) return "less5CbmPrice";
    if (volumeInCBM < 7) return "less7CbmPrice";
    if (volumeInCBM < 10) return "less10CbmPrice";
    return "geq10CbmPrice";
  }

}

class CustomerTruckContainerTransportChargeCalculator extends CustomerTransportChargeCalculator {

  calculate(ctx: ICalculatorContext, customerTruckCharge: any) {
    if (!customerTruckCharge) return;
    try {
      if (TruckType.CONTAINER != customerTruckCharge.truckType) {
        throw new Error("Expect Customer Truck Container Transport Charge!");
      }

      customerTruckCharge.chargeableWeightInKG = ctx.grossWeightInKG
      customerTruckCharge.chargeableVolumeInCBM = ctx.volumeInCbm;

      this.calculateTotalCharge(customerTruckCharge);
      this.calculateAdditionalCharges(ctx, customerTruckCharge, customerTruckCharge.additionalCharges);
      this.calculateFinalCharge(customerTruckCharge);
      this.calculateCommissions(ctx, customerTruckCharge, customerTruckCharge.commissionDistributions);
      this.calculateDomesticCharge(ctx, customerTruckCharge);
    } catch (error) {
      console.log(error);
      alert(`Can't calculate for Truck Container Transport Charge : \n${error}`);
    }
  }

  calculateTotalCharge(charge: any) {
    let totalCharge =
      this.calculateContainerPrice(charge, 'regular20Quantity', 'regular20Price') +
      this.calculateContainerPrice(charge, 'regular40Quantity', 'regular40Price') +
      this.calculateContainerPrice(charge, 'regular45Quantity', 'regular45Price') +
      this.calculateContainerPrice(charge, 'reefer20Quantity', 'reefer20Price') +
      this.calculateContainerPrice(charge, 'reefer40Quantity', 'reefer40Price') +
      this.calculateContainerPrice(charge, 'reefer45Quantity', 'reefer45Price') +
      this.calculateContainerPrice(charge, 'flatbed20Quantity', 'flatbed20Price') +
      this.calculateContainerPrice(charge, 'flatbed40Quantity', 'flatbed40Price') +
      this.calculateContainerPrice(charge, 'flatbed45Quantity', 'flatbed45Price') +
      this.calculateContainerPrice(charge, 'platform20Quantity', 'platform20Price') +
      this.calculateContainerPrice(charge, 'platform40Quantity', 'platform40Price') +
      this.calculateContainerPrice(charge, 'platform45Quantity', 'platform45Price') +
      this.calculateContainerPrice(charge, 'Tilt20Quantity', 'tilt20Price') +
      this.calculateContainerPrice(charge, 'tilt40Quantity', 'tilt40Price') +
      this.calculateContainerPrice(charge, 'tilt45Quantity', 'tilt45Price');

    charge.shipmentTotalCharge = totalCharge;
    charge.shipmentTotalTax = totalCharge * charge.taxRate;
  };
}

class CustomerTruckRegularTransportChargeCalculator extends CustomerTransportChargeCalculator {
  calculate(ctx: ICalculatorContext, customerTruckCharge: any) {
    if (!customerTruckCharge) return;
    try {
      if (TruckType.REGULAR !== customerTruckCharge.truckType) throw "Expect Customer Truck Regular Transport Charge!"
      customerTruckCharge.chargeableWeightInKG = ctx.grossWeightInKG
      customerTruckCharge.chargeableVolumeInCBM = ctx.volumeInCbm;

      this.calculateTotalCharge(customerTruckCharge);
      this.calculateAdditionalCharges(ctx, customerTruckCharge, customerTruckCharge.additionalCharges);
      this.calculateFinalCharge(customerTruckCharge);
      this.calculateCommissions(ctx, customerTruckCharge, customerTruckCharge.commissionDistributions);
      this.calculateDomesticCharge(ctx, customerTruckCharge);
    } catch (err) {
      console.log(err);
      alert(`Can't calculate for Truck Regular Transport Charge : \n${err}`);
    }
  }

  calculateTotalCharge(charge: any) {
    let totalCharge =
      this.calculateContainerPrice(charge, 'truck500kgsQuantity', 'truck500kgsPrice') +
      this.calculateContainerPrice(charge, 'truckTonQuantity', 'truckTonPrice') +
      this.calculateContainerPrice(charge, 'truck2TonQuantity', 'truck2TonPrice') +
      this.calculateContainerPrice(charge, 'truck3Ton5Quantity', 'truck3Ton5Price') +
      this.calculateContainerPrice(charge, 'truck5TonQuantity', 'truck5TonPrice') +
      this.calculateContainerPrice(charge, 'numberOfTruck8Ton', 'truck8TonPrice') +
      this.calculateContainerPrice(charge, 'truck10TonQuantity', 'truck10TonPrice') +
      this.calculateContainerPrice(charge, 'truck15TonQuantity', 'truck15TonPrice') +
      this.calculateContainerPrice(charge, 'truck20TonQuantity', 'truck20TonPrice') +
      this.calculateContainerPrice(charge, 'truck25TonQuantity', 'truck25TonPrice') +
      this.calculateContainerPrice(charge, 'truck30TonQuantity', 'truck30TonPrice');

    charge.shipmentTotalCharge = totalCharge;
    charge.shipmentTotalTax = totalCharge * charge.taxRate;
  };
}

class CustomerCustomClearanceAirCalculator extends CustomerTransportChargeCalculator {
  calculate(ctx: ICalculatorContext, clearance: any) {
    if (!clearance) return;
    if (CustomClearanceType.AIR != clearance.type) throw new Error("Expect Customer Custom Clearance for Air!");
    this.calculateAdditionalCharges(ctx, clearance, clearance.additionalCharges);
    clearance.shipmentTotalCharge = clearance.airPrice;
    clearance.shipmentTotalTax = clearance.airPrice * clearance.taxRate;
    this.calculateCommissions(ctx, clearance, clearance.commissionDistributions);
    this.calculateFinalCharge(clearance);
    this.calculateDomesticCharge(ctx, clearance);
  }
}

class CustomerCustomClearanceSeaLCLCalculator extends CustomerTransportChargeCalculator {
  calculate(ctx: ICalculatorContext, clearance: any) {
    if (!clearance) return;
    if (CustomClearanceType.SEA_LCL != clearance.type) throw new Error("Expect Customer Custom Clearance for Sea LCL!");
    this.calculateAdditionalCharges(ctx, clearance, clearance.additionalCharges);
    clearance.shipmentTotalCharge = clearance.seaLCLPrice;
    clearance.shipmentTotalTax = clearance.seaLCLPrice * clearance.taxRate;
    this.calculateFinalCharge(clearance);
    this.calculateCommissions(ctx, clearance, clearance.commissionDistributions);
    this.calculateDomesticCharge(ctx, clearance);
  }
}

class CustomerCustomClearanceSeaFCLCalculator extends CustomerTransportChargeCalculator {
  calculate(ctx: ICalculatorContext, clearance: any) {
    if (!clearance) return;
    if (CustomClearanceType.SEA_FCL != clearance.type) throw new Error("Expect Customer Custom Clearance for Sea FCL!");
    this.calculateAdditionalCharges(ctx, clearance, clearance.additionalCharges);
    this.calculateTotalCharge(clearance);
    this.calculateFinalCharge(clearance);
    this.calculateCommissions(ctx, clearance, clearance.commissionDistributions);
    this.calculateDomesticCharge(ctx, clearance);
  }

  calculateTotalCharge(clearance: any) {
    let totalCharge = 0;
    totalCharge += clearance.container20Quantity * clearance.container20Price
    totalCharge += clearance.container40Quantity * clearance.container40Price
    clearance.shipmentTotalCharge = totalCharge;
    clearance.shipmentTotalTax = totalCharge * clearance.taxRate;
  }
}

const calculator = {
  air: new AirQuoteCalculator(),
  seaLCL: new SeaLCLQuoteCalculator(),
  seaFCL: new CustomerSeaFCLTransportChargeCalculator(),
  truckContainer: new CustomerTruckContainerTransportChargeCalculator(),
  truckRegular: new CustomerTruckRegularTransportChargeCalculator(),
  customClearanceAir: new CustomerCustomClearanceAirCalculator(),
  customClearanceSeaLCL: new CustomerCustomClearanceSeaLCLCalculator(),
  customClearanceSeaFCL: new CustomerCustomClearanceSeaFCLCalculator(),
  additionalCharge: new AdditionalChargeCalculator(),
  commission: new CommissionDistributionCalculator(),
};

export { calculator }
