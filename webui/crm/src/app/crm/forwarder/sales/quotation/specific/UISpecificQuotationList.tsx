import React from 'react';
import * as FeatherIcon from "react-feather";

import { util, grid, sql, bs, app, server, entity, input } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";
import { app as lgc_app } from '@datatp-ui/logistics';

import { T } from '../../backend'

import { UIQuotationUtils } from '../QuotationUtils';

import { WRateFinderGridFilter } from 'app/crm/forwarder/price';

import _settings = lgc_app.logistics.settings;
import mapToTypeOfShipment = _settings.mapToTypeOfShipment;
import TransportationMode = _settings.TransportationMode;
import TransportationTool = _settings.TransportationTool;

const SESSION = app.host.DATATP_SESSION;

export class UISpecificQuotationPlugin extends entity.DbEntityListPlugin {

  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      context: 'company',
      service: 'QuotationService',
      searchMethod: 'searchSpecificQuotations',
      changeStorageStateMethod: 'changeSpecificQuotationStorageState',
    }

    const today = new Date();
    let requestDateRange = new util.TimeRange();
    requestDateRange.fromSetDate(today).fromSubtract(2, "month").fromStartOf("month"); // -3 months
    requestDateRange.toSetDate(today).toEndOf("month");

    this.searchParams = {
      params: { "space": space, },
      filters: [
        ...sql.createSearchFilter()
      ],
      optionFilters: [
        sql.createStorageStateFilter([
          entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED
        ]),
      ],
      rangeFilters: [
        ...sql.createDateTimeFilter("requestDate", T("Request Date"), requestDateRange),
      ],
      maxReturn: 1000
    }

  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }
}

interface UISpecificQuotationListProps extends entity.DbEntityListProps {
  space: 'User' | 'Company' | 'System'
}
export class UISpecificQuotationList extends entity.DbEntityList<UISpecificQuotationListProps> {

  createVGridConfig(): grid.VGridConfig {
    let { pageContext, space } = this.props;

    let writeCap = pageContext.hasUserWriteCapability();

    let modCap = pageContext.hasUserModeratorCapability();

    const copyAction = {
      name: 'copy', hint: T('Copy'), icon: FeatherIcon.Copy,
      onClick: (ctx: grid.VGridContext, record: grid.DisplayRecord) => {
        let uiList = ctx.uiRoot as UISpecificQuotationList;
        uiList.onCopyRecord(record);
      },
    }

    const toggleSelectAllRow = (ctx: grid.VGridContext, checked: boolean) => {
      let state = grid.VGridConfigUtil.getRecordConfigState(ctx.config);
      state.selectAll = !state.selectAll;
      ctx.model.getDisplayRecordList().markSelectAllDisplayRecords(checked);
      ctx.getVGrid().forceUpdateView();
    }

    const onToggle = (ctx: grid.VGridContext, row: number, check: boolean) => {
      let displayRecordList: grid.IDisplayRecordList = ctx.model.getDisplayRecordList();
      displayRecordList.markSelectDisplayRecord(row, check)
      let dRecord: grid.DisplayRecord = displayRecordList.getDisplayRecordAt(row);
      if (dRecord.indentLevel === 0) {
        let nextRow = row + 1;
        let nextDRecord = displayRecordList.getDisplayRecordAt(nextRow);
        while (nextDRecord && nextDRecord.indentLevel > dRecord.indentLevel) {
          displayRecordList.markSelectDisplayRecord(nextRow, check);
          nextRow++;
          nextDRecord = displayRecordList.getDisplayRecordAt(nextRow);
        }
      }
      ctx.getVGrid().forceUpdateView();
    }

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 35,
        control: {
          width: 30,
          items: writeCap && space === 'User' ? [copyAction] : [],
        },
        fields: [
          {
            name: '_selector_', label: 'Sel', width: 30, cssClass: 'cell-text-center',
            container: 'fixed-left', removable: false, resizable: false,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
              let model = ctx.model;
              let selected = model.getDisplayRecordList().isSelectDisplayRecord(dRecord.row);
              return (
                <input.WCheckboxInput className='d-inline-block m-auto'
                  name='row_selector' checked={selected}
                  onInputChange={(check: boolean) => onToggle(ctx, dRecord.row, check)} focus={focus} />
              )
            },
            customHeaderRender: (ctx: grid.VGridContext, field: grid.FieldConfig, _headerEle: any) => {
              let state = grid.VGridConfigUtil.getRecordConfigState(ctx.config);
              return (<div className='flex-hbox justify-content-center'>
                <input.WCheckboxInput className='m-0 p-0' name='selectAll' checked={state.selectAll}
                  onInputChange={(checked: boolean) => toggleSelectAllRow(ctx, checked)} />
              </div>)
            }
          },
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          // {
          //   name: 'referenceCode', label: T('Ref'), width: 130, filterable: true, container: 'fixed-left',
          //   onClick: (ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          //     this.onDefaultSelect(dRec);
          //   },
          // },
          {
            name: 'clientLabel', label: T('Customer/ Lead Name'), width: 280, filterable: true,
            fieldDataGetter(record) {
              return record['clientLabel'] || 'N/A';
            },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let val = 'N/A';
              let viewName = this.vgridContext.config.view.currentViewName;
              const onCollapseRecord = (dRecord: grid.DisplayRecord) => {
                dRecord.model['collapse'] = !dRecord.model['collapse'];
                let displayRecordList = ctx.model.getDisplayRecordList();
                if (displayRecordList instanceof grid.TreeDisplayModel) {
                  displayRecordList.updateDisplayRecords();
                  ctx.getVGrid().forceUpdateView();
                }
              }


              if (dRec.record[field.name]) {
                val = util.text.formater.uiTruncate(dRec.record[field.name], 280, true);
                if (viewName === 'tree' && dRec.record['groupType'] === 'Code') {
                  val = dRec.record['referenceCode'] || 'N/A';
                }
              }
              if (viewName === 'tree' && dRec.record['groupType'] === 'Client') {
                return <div className="flex-hbox pe-1 me-1" onClick={() => onCollapseRecord(dRec)}>{val}</div>
              }
              return (<button type="button" className="btn btn-link" onClick={() => this.onDefaultSelect(dRec)}>{val}</button>)
            }
          },

          {
            name: 'typeOfShipment', label: T('Type of shpt'), width: 140, filterable: true,
            fieldDataGetter(record: any) {
              if (record['typeOfShipment'] === '-') return record['typeOfShipment'];
              return mapToTypeOfShipment(record['purpose'], record['mode'])
            },
          },
          {
            name: 'containerTypes', label: T(`Vol`), width: 170,
            fieldDataGetter(record: any) {
              let val: string = record['containerTypes']
              if (val) return val;
              let mode: TransportationMode = record['mode']
              if (TransportationTool.isAir(mode)) return record['grossWeightKg'] + ' KGS'
              if (TransportationTool.isSeaLCL(mode)) return record['volumeCbm'] + ' CBM'
            },
          },
          { name: 'incoterms', label: T('Term'), width: 100 },
          {
            name: 'fromLocationLabel', label: T('Port of Loading'), width: 200, cssClass: 'pe-1',
            filterableType: 'string', filterable: true,
          },
          {
            name: 'toLocationLabel', label: T('Port of Discharge'), width: 200, cssClass: 'pe-1',
            filterableType: 'string', filterable: true,
          },
          {
            name: "salemanLabel", label: 'Saleman.', width: 220, filterable: true, filterableType: 'string',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;
              let employeeName: string = record['salemanLabel'] || 'N/A';
              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{employeeName}</div>
                </div>
              )
            },
          },
          {
            name: 'requestDate', label: T('Request Date'),
            width: 120, cssClass: 'text-right',
            fieldDataGetter: (record: any) => {
              if (record['requestDate'] === '-') return record['requestDate'];
              return util.text.formater.compactDate(record['requestDate']);
            },
          },
          {
            name: 'cargoReadyDate', label: T('CRD.'), hint: 'Cargo Ready Date', width: 120, state: { visible: false },
            format: util.text.formater.compactDate
          },
          {
            name: 'estimatedTimeDeparture', label: T('ETD'), width: 140, state: { visible: false },
            format: util.text.formater.compactDate
          },
          {
            name: 'descOfGoods', label: T('Desc Of Goods.'), width: 250, state: { visible: false },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record = dRecord.record;
              let val = record['descOfGoods'] || '-'
              return (
                <div className="flex-hbox justify-content-between align-items-center px-1">
                  <div className="flex-hbox justify-content-center pe-1 me-1">{val}</div>
                </div>
              )
            },
          },
          { name: 'grossWeight', label: T('GW'), width: 100, state: { visible: false } },
          { name: 'volume', label: T('CBM'), width: 100, state: { visible: false } },
          { name: 'unitOfPackage', label: T('Unit Of Package'), width: 180, state: { visible: false } },
          {
            name: 'modifiedTime', label: T('Modified Time'), width: 170,
            filterableType: 'Date', filterable: true,
            fieldDataGetter: (record: any) => {
              if (record['modifiedTime'] === '-') return record['modifiedTime'];
              return util.text.formater.compactDateTime(record['modifiedTime']);
            },
          },
        ],
      },
      toolbar: {
        hide: true,
      },
      footer: {
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T('Select'), this.props.type)
      },
      view: {
        currentViewName: 'tree',
        availables: {
          table: { viewMode: 'table' },
          tree: {
            viewMode: 'tree',
            label: 'Tree View',
            treeField: 'clientLabel',
            plugin: new SpecificQuotationTreePlugin()
          }
        }
      },
    };
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let quotation = dRecord.record;
    let quotationId = quotation['id']
    const { appContext, pageContext } = this.props;
    let viewName = this.vgridContext.config.view.currentViewName;
    if (viewName === 'tree') quotationId = quotation['referenceEntityId'];

    if (!quotationId) {
      let message = (<div className="ms-1 text-warning py-3 border-bottom">Unknown Quotation ID!!!</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return;
    }

    const accessAccountId = SESSION.getAccountId();

    if (accessAccountId === quotation['salemanAccountId'] || pageContext.hasUserModeratorCapability()) {
      if (pageContext.hasUserModeratorCapability()) {
        appContext.addOSNotification('warning', "You are viewing quotation as moderator.");
      }
      UIQuotationUtils.showUISQuotationById(this, quotationId);
    } else {
      let message = (
        <div className="ms-1 text-warning py-3 border-bottom">
          Access denied. This quotation belongs to another salesperson.
        </div>
      );
      bs.dialogShow('Access Restricted', message, { backdrop: 'static', size: 'md' });
      return;
    }

  }

  onCopyRecord(dRecord: grid.DisplayRecord) {
    const { appContext, pageContext } = this.props;
    let viewName = this.vgridContext.config.view.currentViewName;
    let quotation = dRecord.record;
    if (viewName === 'tree' && quotation['groupType'] === 'Client') return
    const accessAccountId = SESSION.getAccountId();
    if (accessAccountId !== quotation['salemanAccountId'] && !pageContext.hasUserModeratorCapability()) {
      let message = (
        <div className="ms-1 text-warning py-3 border-bottom">
          Access denied. You can only clone your own quotations.
        </div>
      );
      bs.dialogShow('Access Restricted', message, { backdrop: 'static', size: 'md' });
      return;
    }
    let id = quotation['id'];
    if (viewName === 'tree') id = quotation['referenceEntityId'];
    appContext.createHttpBackendCall('QuotationService', 'copySpecificQuotation', { quotationId: id })
      .withSuccessData((data: any) => {
        appContext.addOSNotification('success', "Clone quotation success!!!");
        UIQuotationUtils.showUISpecificQuotation(this, data);
      })
      .withFail((response: server.BackendResponse) => {
        let title = T('Clone Quotation Failed!');
        appContext.addOSNotification('danger', title, response.error, response);
        let message = response.error.message || title;
        bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        return;
      })
      .call();
  }

  deleteRecords(ids: Array<number>) {
    let records = this.vgridContext.model.getRecords();
    let holder = []
    for (let record of records) {
      if (!ids.includes(record['id'])) {
        holder.push(record);
      }
    }
    this.vgridContext.model.replaceWith(holder);
  }

  getSelectedRecords(viewName: string) {

    let dRecords = this.vgridContext.model.getDisplayRecordList().getDisplayRecords();
    let records = [];
    for (let dRecord of dRecords) {
      let record = dRecord.record
      let state = grid.getRecordState(record);
      if ((viewName === 'table' && state.selected) || (viewName === 'tree' && state.selected && record['referenceEntityId'])) {
        records.push(record);
      }
    }

    return records;
  }

  onDeleteAction(): void {

    const { appContext, pageContext } = this.props;
    let viewName = this.vgridContext.config.view.currentViewName;

    const quotations = this.getSelectedRecords(viewName);

    const accessAccountId = SESSION.getAccountId();
    let isOwner = quotations.every(record => record.salemanAccountId === accessAccountId);

    if (isOwner || pageContext.hasUserModeratorCapability()) {

      let selectedIds = quotations.map(record => record.id);
      if (viewName === 'tree') {
        selectedIds = quotations.map(record => record.referenceEntityId);
      }
      if (selectedIds.length === 0) {
        bs.dialogShow('Message',
          <div className="text-warning text-center p-2">
            Please select at least one quotation Selected!
          </div>
        );
        return;
      }

      appContext.createHttpBackendCall('QuotationService', 'deleteByIds', { ids: selectedIds })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification('success', T('Delete Specific Quotation Success!'));
          this.deleteRecords(selectedIds);
          this.getVGridContext().getVGrid().forceUpdateView();
        })
        .call();

    } else {
      bs.dialogShow('Message',
        <div className="ms-1 text-warning py-3 border-bottom">
          Access denied. You can only delete your own quotations.
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    plugin.searchParams = searchParam;
    this.reloadData();
    this.nextViewId();
    // this.forceUpdate();
    this.vgridContext.getVGrid().forceUpdateView();
  };

  render() {
    if (this.isLoading()) return this.renderLoading();
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1 mt-1'>

          <div className='flex-hbox justify-content-start align-items-center'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={searchParam} field={"maxReturn"}
                options={[1000, 2000, 5000, 10000]}
                optionLabels={['Show 1000 records', 'Show 2000 records', 'Show 5000 records', 'Show 10.000 records']}
                onInputChange={this.onModify} />
            </div>
            <WRateFinderGridFilter context={this.vgridContext} />
          </div>

          <div className="flex-hbox justify-content-end align-items-center flex-grow-1">
            <bs.Button laf='warning' className="border-0 p-1 border-end rounded-0" outline
              onClick={() => this.onDeleteAction()}>
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button>
          </div>

        </div>
        <div key={this.viewId} className='flex-vbox'>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }
}


class SpecificQuotationTreePlugin extends grid.TreeDisplayModelPlugin {

  override setCollapse(record: grid.TreeRecord) {
    record.collapse = true;
  }

  buildTreeRecords(records: Array<any>): Array<grid.TreeRecord> {
    let treeRecords: Array<any> = [];
    let idCounter = 1;

    let clientGroups: util.ListRecordMap<string> = new util.ListRecordMap<string>();
    clientGroups.addAllRecords('clientLabel', records);

    for (let clientLabel of clientGroups.getListNames()) {
      let records: Array<any> = clientGroups.getList(clientLabel);

      let clientNode: any = {
        id: idCounter++,
        parentId: undefined,
        referenceEntityId: undefined,
        groupType: 'Client',
        clientLabel: records[0].clientLabel,
        typeOfShipment: '-',
        containerTypes: '-',
        incoterms: '-',
        fromLocationLabel: '-',
        toLocationLabel: '-',
        salemanLabel: '-',
        requestDate: '-',
        cargoReadyDate: '-',
        estimatedTimeDeparture: '-',
        descOfGoods: '-',
        grossWeight: '-',
        volume: '-',
        unitOfPackage: '-',
        modifiedTime: '-',
      }
      treeRecords.push(clientNode);

      records.forEach((record, _index, _records) => {
        const codeNode = {
          id: idCounter++,
          parentId: clientNode.id,
          referenceEntityId: record.id,
          groupType: 'Code',
          clientLabel: record.clientLabel,
          referenceCode: record.referenceCode,
          containerTypes: record.containerTypes,
          purpose: record.purpose,
          mode: record.mode,
          grossWeightKg: record.grossWeightKg,
          volumeCbm: record.volumeCbm,
          incoterms: record.incoterms,
          fromLocationLabel: record.fromLocationLabel,
          toLocationLabel: record.toLocationLabel,
          salemanLabel: record.salemanLabel,
          salemanAccountId: record.salemanAccountId,
          requestDate: record.requestDate,
          cargoReadyDate: record.cargoReadyDate,
          estimatedTimeDeparture: record.estimatedTimeDeparture,
          descOfGoods: record.descOfGoods,
          grossWeight: record.grossWeight,
          volume: record.volume,
          unitOfPackage: record.unitOfPackage,
          modifiedTime: record.modifiedTime,
        }
        treeRecords.push(codeNode);
      })

    }
    grid.initRecordStates(treeRecords);

    return super.buildTreeRecords(treeRecords);
  }

}


export class UISpecificQuotationPage extends UISpecificQuotationList {

  onExpandQuotation = () => {
    let { pageContext } = this.props;

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<UISpecificQuotationList appContext={appCtx} pageContext={pageCtx} space='User'
        plugin={new UISpecificQuotationPlugin('User')} />)
    }

    let popupId = `view-expand-squotation-${util.IDTracker.next()}`;
    let pupupLabel: string = `Quotations`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  render(): React.JSX.Element {
    const { pageContext } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    return (
      <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center h-100"
        style={{
          minWidth: 900,
          borderColor: borderColor,
          transition: 'all 0.3s ease',
          marginBottom: '10px'
        }}
        onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
        onMouseLeave={(e) => {
          e.currentTarget.style.boxShadow = 'none';
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.backgroundColor = '#fff';
        }}>
        <div className="flex-vbox bg-white rounded-md w-100 h-100">

          <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
            <h5 style={{ color: '#6c757d' }}><FeatherIcon.HelpCircle className="me-2" size={16} />{`Quotation List`}</h5>

            <div className="flex-hbox justify-content-end align-items-center gap-1">
              <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
                onClick={this.onExpandQuotation}>
                <FeatherIcon.Maximize2 size={14} className="me-1" />
                Expand
              </bs.Button>

            </div>
          </div>

          <div key={this.viewId} className='flex-vbox'>
            {this.renderUIGrid()}
          </div>

        </div>
      </div>
    );
  }
}