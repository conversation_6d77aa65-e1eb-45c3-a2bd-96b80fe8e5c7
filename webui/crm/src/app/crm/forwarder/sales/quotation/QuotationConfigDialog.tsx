import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, input } from '@datatp-ui/lib';
import { app as lgc_app } from '@datatp-ui/logistics';
import { ContainerType, ContainerTypeUnit } from '../../common/ContainerTypeUtil';
import { BBContainerType } from '../../common/BBContainerInput';

import TransportationMode = lgc_app.logistics.settings.TransportationMode;
import TransportationTool = lgc_app.logistics.settings.TransportationTool;

export const LOCAL_CHARGES: Record<string, any[]> = {
  SEA_FCL_IMPORT: [
    {
      target: 'DESTINATION',
      code: 'S_DF',
      name: 'DOC FEE',
      unit: 'SET'
    },
    {
      target: 'DESTINATION',
      code: 'S_HAND',
      name: 'HANDLING FEE',
      unit: 'SHIPMENT'
    },
    {
      target: 'DESTINATION',
      code: 'S_THC',
      name: 'THC',
      unit: ''
    },
    {
      target: 'DESTINATION',
      code: 'S_C<PERSON>',
      name: 'C<PERSON>',
      unit: ''
    },
    {
      target: 'DESTINATION',
      code: 'S_CLEAN',
      name: 'CLEANING FEE',
      unit: ''
    }
  ],
  SEA_FCL_EXPORT: [
    {
      target: 'ORIGIN',
      code: 'S_THC',
      name: 'THC',
      unit: ''
    },
    {
      target: 'ORIGIN',
      code: 'S_DF',
      name: 'DOC FEE',
      unit: ''
    },
    {
      target: 'ORIGIN',
      code: 'S_SEAL',
      name: 'SEAL FEE',
      unit: ''
    }
  ],
  SEA_LCL_EXPORT: [
    {
      target: 'ORIGIN',
      code: 'S_CFS',
      name: 'CFS',
      unit: 'CBM'
    },
    {
      target: 'ORIGIN',
      code: 'S_THC',
      name: 'THC',
      unit: 'CBM'
    },
    {
      target: 'ORIGIN',
      code: 'S_LSS',
      name: 'LOW SULPHUR SURCHARGE',
      unit: 'CBM'
    },
    {
      target: 'ORIGIN',
      code: 'S_SEAL',
      name: 'SEAL FEE',
      unit: 'CBM'
    },
    {
      target: 'ORIGIN',
      code: 'S_BILL',
      name: 'BILL FEE',
      unit: 'BL'
    },
    {
      target: 'ORIGIN',
      code: 'S_TLX',
      name: 'TELEX RELEASED FEE',
      unit: 'BL'
    },
    {
      target: 'ORIGIN',
      code: 'S_HAND',
      name: 'HANDLING FEE',
      unit: 'SHIPMENT'
    },
  ],
  SEA_LCL_IMPORT: [
    {
      target: 'DESTINATION',
      code: 'S_CFS',
      name: 'CFS',
      unit: 'CBM'
    },
    {
      target: 'DESTINATION',
      code: 'S_THC',
      name: 'THC',
      unit: 'CBM'
    },
    {
      target: 'DESTINATION',
      code: 'S_CIC',
      name: 'CIC',
      unit: ''
    },
    {
      target: 'DESTINATION',
      code: 'S_DF',
      name: 'DOC FEE',
      unit: 'SET'
    },
    {
      target: 'DESTINATION',
      code: 'S_HAND',
      name: 'HANDLING FEE',
      unit: 'SHIPMENT'
    },
  ],

};

export const DOMESTIC_CHARGES = [
  {
    target: 'DESTINATION',
    mode: TransportationMode.TRUCK_CONTAINER,
    code: ' S_TRUCK',
    name: 'DOMESTIC TRUCKING FEE',
    unit: '',
  },
  {
    target: 'DESTINATION',
    mode: TransportationMode.UNKNOWN,
    code: 'S_CUSTOM1',
    name: 'CUSTOMS CLEARANCE FEE',
    unit: ''
  }
]

export interface UIQuotationConfigConfirmProps extends bs.ELEProps {
  quotation: any;
  onConfirm: (quotation: any) => void;
  onCancel: (quotation: any) => void;
}

interface UIQuotationConfigConfirmState {
  selectedLocalCharges: Record<string, boolean>;
  selectedDomesticCharges: Record<string, boolean>;
  selectedContainerType: ContainerType[]
}

export class UIQuotationConfigConfirm extends React.Component<UIQuotationConfigConfirmProps, UIQuotationConfigConfirmState> {
  purpose: 'IMPORT' | 'EXPORT';
  mode: TransportationMode

  state: UIQuotationConfigConfirmState = {
    selectedLocalCharges: {},
    selectedDomesticCharges: {},
    selectedContainerType: []
  };

  constructor(props: UIQuotationConfigConfirmProps) {
    super(props);
    const { quotation } = this.props;
    let inquiry = quotation['inquiry'] || {};
    this.purpose = inquiry['purpose'] || 'EXPORT';
    this.mode = inquiry['mode']
  }

  componentDidMount() {
    let key = this.mode + '_' + this.purpose;
    const charges = LOCAL_CHARGES[key];

    // Initialize all charges as selected
    const selectedLocalCharges = charges.reduce((acc, charge) => {
      acc[charge.code] = true;
      return acc;
    }, {} as Record<string, boolean>);
    this.setState({ selectedLocalCharges });
  }

  private getLocalCharges(): any[] {
    const { selectedLocalCharges } = this.state;
    const key = this.mode + '_' + this.purpose;
    const charges = LOCAL_CHARGES[key];

    return charges
      .filter(charge => selectedLocalCharges[charge.code])
      .map(charge => ({
        ...charge,
        mode: this.mode,
        currency: 'USD',
        quantity: '',
        unitPrice: '',
        totalAmount: '',
        note: '',
        quoteRate: {}
      }));
  }

  onToggleCharge = (code: string) => {
    this.setState(prevState => ({
      selectedLocalCharges: {
        ...prevState.selectedLocalCharges,
        [code]: !prevState.selectedLocalCharges[code]
      }
    }));
  }

  onSelectAll = (checked: boolean) => {
    const key = this.mode + '_' + this.purpose;
    const charges = LOCAL_CHARGES[key];
    const selectedLocalCharges = charges.reduce((acc, charge) => {
      acc[charge.code] = checked;
      return acc;
    }, {} as Record<string, boolean>);
    this.setState({ selectedLocalCharges });
  }

  private processQuotationWithLocalCharges(localCharges: any[]): any {
    const { quotation } = this.props;
    let inquiry: any = quotation['inquiry'] || {};
    let containerType: string = inquiry['volumeInfo'] || '';
    let containers = ContainerTypeUnit.textToContainerList(containerType);
    inquiry['containers'] = containers;
    quotation['quoteListSelector'] = quotation['quoteList'] || [];

    let existingCharges: any[] = quotation['localHandlingCharges'] || [];
    const existingCodes = new Set(existingCharges.map(charge => charge.code));
    const newLocalCharges = localCharges.filter(charge => !existingCodes.has(charge.code));
    existingCharges.push(...newLocalCharges);
    quotation['localHandlingCharges'] = existingCharges;
    return quotation;
  }

  onConfirm = (evt: React.MouseEvent) => {
    const { onConfirm } = this.props;
    const localCharges = this.getLocalCharges();
    const domesticCharges: any[] = this.getDomesticCharges();
    const updatedQuotation = this.processQuotationWithLocalCharges([...localCharges, ...domesticCharges]);
    let triggerEle = evt.target as HTMLElement;
    bs.BootstrapUtil.dialogHide(triggerEle);
    onConfirm(updatedQuotation);
  }

  onCancel = (evt: React.MouseEvent) => {
    const { onCancel, quotation } = this.props;
    let triggerEle = evt.target as HTMLElement;
    bs.BootstrapUtil.dialogHide(triggerEle);
    onCancel(quotation);
  }

  onChangeContainer = (bean: any, field: string, oldVal: any, newVal: any) => {
    if (newVal === oldVal) {
      return;
    }

    if (!newVal || newVal === '') {
      bs.dialogShow('Message',
        <div className="text-warning text-center p-2">
          {`Please provide Container Types.`}
        </div>
      );
    }

    bean['containers'] = ContainerTypeUnit.textToContainerList(newVal);
    this.props.quotation['inquiry'] = bean;
    this.forceUpdate();
  }

  onToggleDomesticCharge = (code: string) => {
    this.setState(prevState => ({
      selectedDomesticCharges: {
        ...prevState.selectedDomesticCharges,
        [code]: !prevState.selectedDomesticCharges[code]
      }
    }));
  }

  private getDomesticCharges(): any[] {
    const { selectedDomesticCharges } = this.state;
    return DOMESTIC_CHARGES
      .filter(charge => selectedDomesticCharges[charge.code])
      .map(charge => ({
        ...charge,
        currency: 'USD',
        quantity: '',
        unitPrice: '',
        totalAmount: '',
        note: '',
        quoteRate: {}
      }));
  }

  onSelectAllDomestic = (checked: boolean) => {
    const selectedDomesticCharges = DOMESTIC_CHARGES.reduce((acc, charge) => {
      acc[charge.code] = checked;
      return acc;
    }, {} as Record<string, boolean>);
    this.setState({ selectedDomesticCharges });
  }

  renderContainerType() {
    if (!TransportationTool.isSeaFCL(this.mode)) return <></>
    let inquiry = this.props.quotation['inquiry'] || {};
    return (
      <div className="mb-2">
        <div className="d-flex align-items-center mb-1">
          <FeatherIcon.Package size={14} className="text-primary me-1" />
          <div className="fw-bold">Container Type</div>
        </div>
        <div className="border rounded p-2 bg-white">
          <BBContainerType bean={inquiry} field={'volumeInfo'} required onInputChange={this.onChangeContainer} />
        </div>
      </div>
    )
  }

  render() {
    const { selectedLocalCharges, selectedDomesticCharges } = this.state;
    const key = this.mode + '_' + this.purpose;
    const charges = LOCAL_CHARGES[key] || [];
    let inquiry = this.props.quotation['inquiry'] || {};
    const allSelected = charges.every(charge => selectedLocalCharges[charge.code]);

    let purpose = inquiry['purpose'] || 'EXPORT';

    return (
      <div className='flex-vbox'>
        <div className="p-2 flex-vbox">

          {this.renderContainerType()}

          <div className="mb-3">
            <div className='d-flex justify-content-between align-items-center p-2 bg-light rounded-top border'>
              <div className="d-flex justify-content-start align-items-center">
                <FeatherIcon.DollarSign size={16} className="text-primary me-2" />
                <div className="fw-bold text-dark">{`Local charge at ${purpose === 'IMPORT' ? 'POD / Destination' : 'POL / Origin'}`}</div>
              </div>

              <div className="d-flex align-items-center justify-content-end">
                <input.WCheckboxInput
                  name="selectAll"
                  checked={allSelected}
                  onInputChange={(checked) => this.onSelectAll(checked)}
                />
                <span className="ms-2 fw-bold text-secondary">Select All</span>
              </div>

            </div>

            <div className="p-3 bg-white border border-top-0 rounded-bottom">
              <div className="d-flex flex-wrap gap-3">
                {charges.map((charge, index) => (
                  <div key={index} className="flex-grow-0" style={{ width: 'calc(33.333% - 0.75rem)' }}>
                    <div className="d-flex align-items-center p-2 rounded hover-bg-light">
                      <input.WCheckboxInput
                        name={charge.code}
                        checked={selectedLocalCharges[charge.code]}
                        onInputChange={() => this.onToggleCharge(charge.code)}
                      />
                      <span className="ms-2 text-dark">{charge.name}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="mb-3">
            <div className='d-flex justify-content-between align-items-center p-2 bg-light rounded-top border'>
              <div className="d-flex justify-content-start align-items-center">
                <FeatherIcon.Truck size={16} className="text-primary me-2" />
                <div className="fw-bold text-dark">{`Domestic Charges ${purpose === 'IMPORT' ? 'in destination' : 'in origin'}`}</div>
              </div>

              <div className="d-flex align-items-center justify-content-end">
                <input.WCheckboxInput
                  name="selectAllDomestic"
                  checked={DOMESTIC_CHARGES.every(charge => selectedDomesticCharges[charge.code])}
                  onInputChange={(checked) => this.onSelectAllDomestic(checked)}
                />
                <span className="ms-2 fw-bold text-secondary">Select All</span>
              </div>
            </div>

            <div className="p-3 bg-white border border-top-0 rounded-bottom">
              <div className="d-flex flex-wrap gap-3">
                {DOMESTIC_CHARGES.map((charge, index) => (
                  <div key={index} className="flex-grow-0" style={{ width: 'calc(50% - 0.75rem)' }}>
                    <div className="d-flex align-items-center p-2 rounded hover-bg-light">
                      <input.WCheckboxInput
                        name={charge.code}
                        checked={selectedDomesticCharges[charge.code] || false}
                        onInputChange={() => this.onToggleDomesticCharge(charge.code)}
                      />
                      <span className="ms-2 text-dark">{charge.name}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

        </div>

        <div className='flex-hbox-grow-0 justify-content-end mt-1 border-top p-2'>
          <bs.Button laf='warning' outline className="px-2 py-1 mx-1" style={{ width: 100 }} onClick={this.onCancel}>
            <FeatherIcon.X size={12} /> Cancel
          </bs.Button>
          <bs.Button laf='info' outline className="px-2 py-1 mx-1" style={{ width: 100 }} onClick={this.onConfirm}>
            <FeatherIcon.Check size={12} /> OK
          </bs.Button>
        </div>
      </div>
    );
  }
}

export function QuotationConfigDialog(quotation: any, onConfirm: (quotation: any) => void, onCancel: (quotation: any) => void) {
  const ui = (
    <UIQuotationConfigConfirm quotation={quotation}
      onConfirm={onConfirm} onCancel={onCancel} />
  );
  bs.dialogShow('Quotation Configuration', ui, { size: 'md' });
}
