---
sidebar_position: 1
hide_table_of_contents: true
displayed_sidebar: userSidebar
---

# CRM

## CRM Flowchart

Quy trình tổng quan.

----------------------------------------------------------------------------------

```text
┌─────────────────┐     ┌───────────────────────┐
│  Khách hàng     │────▶│  Sales tiếp nhận      │
│  gửi yêu cầu    │     │  thông tin            │
└─────────────────┘     └──────────┬────────────┘
                                   │
                                   ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Kiểm tra giá trên hệ thống                         │
└───────────┬─────────────────────────┬─────────────────────┬─────────────────┘
            │                         │                     │
            ▼                         ▼                     ▼
┌───────────────────────┐  ┌───────────────────┐  ┌───────────────────────────┐
│  Giá đã có sẵn        │  │ Giá đã có sẵn     │  │  Giá chưa có sẵn          │
│  cần xác nhận lại     │  │ không cần xác nhận│  │                           │
└──────────┬────────────┘  └─────────┬─────────┘  └───────────┬───────────────┘
           │                         │                        │
           ▼                         ▼                        ▼
┌───────────────────────┐  ┌───────────────────┐  ┌───────────────────────────┐
│  Request Pricing      │  │ Request a Quote   │  │  Request Pricing          │
│  ở từng dòng giá      │  │ trên thanh công cụ│  │  trên thanh công cụ       │
└──────────┬────────────┘  └─────────┬─────────┘  └───────────┬───────────────┘
           │                         │                        │
           ▼                         ▼                        ▼
┌───────────────────────┐  ┌───────────────────┐  ┌───────────────────────────┐
│  Tạo Inquiry Request  │  │ Tạo Inquiry       │  │  Tạo Inquiry Request      │
│  (tự động gửi email)  │  │ Request (không    │  │  (tự động gửi email)      │
└──────────┬────────────┘  │ gửi mail)         │  └───────────┬───────────────┘
           │               └─────────┬─────────┘              │
           ▼                         │                        ▼
┌───────────────────────┐            │            ┌───────────────────────────┐
│  Bộ phận Pricing      │            │            │  Bộ phận Pricing          │
│  cập nhật thông tin   │            │            │  cập nhật thông tin       │
└──────────┬────────────┘            │            └───────────┬───────────────┘
           │                         │                        │
           ▼                         ▼                        ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                             Tạo Quotation (Báo giá)                       │
└───────────────────────────────────────┬───────────────────────────────────┘
                                        │
                                        ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                      Gửi báo giá cho khách hàng                           │
└───────────────────────────────────────┬───────────────────────────────────┘
                                        │
                                        ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                           Tạo Internal Booking                            │
└───────────────────────────────────────┬───────────────────────────────────┘
                                        │
                                        ▼
┌───────────────────────────────────────────────────────────────────────────┐
│              Cập nhật Inquiry Request thành trạng thái "won"              │
└───────────────────────────────────────────────────────────────────────────┘
```

#### 1. Tiếp nhận yêu cầu từ khách hàng.
- Sales tiếp nhận thông tin từ khách hàng.

#### 2. Kiểm tra và xử lý giá trên CRM/Hệ thống.
  Sales kiểm tra giá trên ứng dụng CRM. [Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/search_prices)

- Trường hợp 1: Giá đã có sẵn và cần xác nhận lại với Pricing.
    - Sales nhấn nút Request Pricing ở từng dòng giá.
    - Hệ thống tự động tạo Inquiry Request và gửi email đến bộ phận Pricing.
- Trường hợp 2: Giá đã có sẵn và không cần xác nhận lại.
    - Sales có thể tạo báo giá trực tiếp bằng cách nhấn nút Request a Quote trên thanh công cụ.
    - Hệ thống tự động tạo Inquiry Request tương ứng (không gửi mail).
- Trường hợp 3: Giá chưa có sẵn
    - Sales sử dụng chức năng Request Pricing trên thanh công cụ.
    - Hệ thống tự động tạo Inquiry Request và gửi email đến bộ phận Pricing.

#### 3. Xử lý yêu cầu báo giá.
- Đối với trường hợp 1 và 3 (giá cần xác nhận hoặc chưa có sẵn):
    Bộ phận Pricing cập nhật thông tin giá.
- Sales tạo báo giá (Quotation) từ request sau khi nhận được thông tin giá.

#### 4. Tạo và gửi báo giá.  [Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/quotation)
- Sales tạo báo giá (Quotation) từ giá có sẵn hoặc giá đã được cập nhật.
- Có thể xuất báo giá ra file Excel hoặc gửi email trực tiếp cho khách hàng (Phần gửi mail đang pending).

#### 5. Xác nhận booking.  [Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/quotation)
- Sales tạo Internal Booking gửi cho Customer Service với thông tin selling đã nhập trước đó.
- Khi gửi Internal Booking thành công:
  - Hệ thống tự động cập nhật trạng thái của Inquiry Request thành "won".
  - Hệ thống tự động gửi thông báo qua zalo cho `Customer Service` về thông tin `Internal Booking`.
  - Hệ thống tự động cập nhật feedback giá bảng, đánh dấu giá đó đã win lô hàng với các thông tin: tên saleman, volume, thông tin hàng.

**Vào module nghiệp vụ**

 - Góc trái màn hình, click chọn ở logo tên công ty -> chọn `CRM`.

![../img/sales/sale_access.gif](./img/crm_access.gif)

#### 1. **Tìm kiếm giá**
  - Cho phép người dùng tìm kiếm giá có sẵn từ hệ thống.

    [Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/search_prices)

#### 2. **Tạo, gửi inquiry request**

   - Cho phép người dùng gửi yêu cầu check giá qua email khi không tìm thấy giá phù hợp trên **Pricing Tools**.

   - Xem lại các y/c đã gửi, cập nhật feedback của khách hàng, trạng thái của request.

    [Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/mail_request)

#### 3. **Tạo, chỉnh sửa báo giá, gửi Internal Booking.**

  - Cho phép người dùng tạo báo giá, export báo giá ra file excel hoặc gửi mail báo giá cho khách hàng.

  - Tạo IB, gửi thông tin qua `BFSOne` cho cus để tiến hành tạo job file

    [Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/quotation)

#### 4. Tạo, quản lý thông tin khách hàng tiềm năng (Lead)

[Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/lead_management)

#### 5. Tạo, quản lý thông tin khách hàng

[Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/customer_management)

```
Thông tin khách hàng được đồng bộ tự động từ hệ thống BFSOne.
Nếu có bất kỳ sai sót hoặc cần cập nhật, vui lòng liên hệ IT để được hỗ trợ.
```

