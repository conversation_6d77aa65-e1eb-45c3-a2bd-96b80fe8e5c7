---
sidebar_position: 4
hide_table_of_contents: true
displayed_sidebar: developerSidebar
---

# CRM Features

### 1. Thông báo những giá mới được update/đang promote.

- Người request: Ms Lụa (Vận hành hàng xuất)
- Người triển khai: Đàn.

- Đặt vấn đề:
  Tối ưu thông tin giá giữa pricing - salemans.
  Hiện tại đang chat qua group zalo, giống như một topic, kênh để pricing và salesman trao đổi về giá, các thay đổi, c<PERSON><PERSON> nh<PERSON>t, gi<PERSON> mới thấp/ cao hơn giá vừa báo, ...
  Hạn chế trong việc group quả tải, spam, cần chia sẽ thông tin toàn hệ thống, không chỉ trong từng group chat riêng.
  <!-- Ngoài việc gửi mail trực tiếp cho salemans đang quan tâm giá tuyến đó, cần có thêm màn hình tracking toàn bộ việc thay đổi giá của pricing để view tổng quan. -->

  Case Study:
    ```
      Sales A check giá tuyến HAIPHONG - SHANGHAI => Báo cho khách.
      Sau đó, pricing cập nhật giá mới cho tuyến HAIPHONG - SHANGHAI, => gửi lại mail cho salesman (người có lịch sử báo giá tuyến đó, ...)
      Hình thức: Tổng hợp lại các tuyến, thông tin gửi 2 lần một ngày.
      - 8AM: Tổng hợp thông tin các tuyến có thay đổi giá từ 14h ngày hôm qua đến trước 8h ngày hôm sau.
      - 14PM : Tổng hợp thông tin các tuyến có thay đổi giá từ 8h đến trước 14h ngày hiện tại.
    ```

- Giải pháp:
  - Theo dõi lịch sử search giá, gửi báo giá theo các tuyến của saleman, theo dõi lịch sử cập nhật, chỉnh sửa giá của pricing
    Từ đó, gửi lại thông tin giá cho salesman (giá tăng, giá giảm so với giá gần nhất, người cập nhật, số lượng cập nhật, chi tiết một số giá mẫu, ...)

  - Tạo thêm một màn hình tổng quan về việc cập nhật giá theo các tuyến của pricing (tuyến nào, giá nào, ngày cập nhật, ai cập nhật, ...)
    để sales có thể theo dõi các giá mới cập nhật trên phần mềm.

  - Ở màn hình tìm kiếm giá:
    Từng line giá nên có icon theo chiều hướng lên/ xuống để thể hiện giá đó đang tăng hay giảm so với lần cập nhật trước hoặc trung bình cộng của kỳ trước (theo rule setup).

### 2. Theo dõi tần suất sử dụng giá, đánh giá hiệu quả của giá, để Pricing cải thiện.

- Người request: Ms Lụa (Vận hành hàng xuất)
- Người triển khai: Đàn.

- Đặt vấn đề:
  - Sales khi lấy giá phần mềm báo cho khách, có feedback thì cập nhật lại trên phần mềm ở mục feedback.
  Feedback này sau khi cập nhật, sẽ mail trực tiếp cho pricing input giá đó.
  Ngoài ra, salesman khác trong toàn hệ thống đều có thể thấy được feedback đó. => IT Done => Thông tin và push sales nhập feedback.
  - IT làm thêm màn hình để tổng hợp các giá có feedback của sales (bao gồm feedback từ giá có sẵn và feedback từ những request check giá gửi cho pricing)
  Nhóm theo các tuyến, người feedback, thông tin feedback.

- Giải pháp:



