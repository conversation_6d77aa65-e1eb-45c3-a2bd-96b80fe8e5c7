---
sidebar_position: 2
hide_table_of_contents: true
displayed_sidebar: developerSidebar
---

# DataTP CRM Backlog

## Tasks

**Sprint Rules:**

- Sprint duration: 1 week (Monday-Sunday)
- Daily status updates required
- Incomplete tasks return to backlog for next sprint prioritization

## Current Sprint
1. [Nhat] Cập nhật document Customer Lead:
  + <PERSON><PERSON><PERSON><PERSON> đổi Lead sang khách hàng.
  + <PERSON><PERSON><PERSON>h<PERSON> hàng, request to BFSOne.

1. [Dan] - Fix bugs, enhance tạo, chỉnh sửa báo giá, internal booking.

3. Theo dõi tần suất sử dụng giá, đánh giá hiệu quả của giá, để Pricing cải thiện.
  - Count số lần giá đó được saleman export.
  - Sau khi tạo Internal Booking thành công -> Cập nhật feedback của bảng giá đó theo format:
      [Tên <PERSON>] - [Ng<PERSON>y Tạo Booking].
      [Incoterm]    - [Volume hàng].
      [<PERSON><PERSON> tả hàng]
  - <PERSON><PERSON><PERSON><PERSON> cron để cập nhật ngày 2 lần.
    - 8AM: Tổ<PERSON> hợp Booking từ 14h ngày hôm qua đến trước 8h ngày hôm sau.
    - 14PM : Tổng hợp Booking từ 8h đến trước 14h ngày hiện tại.
    Từ Internal Booking -> Quotation -> QuotationCharge -> Reference Code.

4. [An] - Migrate dữ liệu bảng giá Quotation qua model mới.
  - Quotation...TransportCharge -> QuotationCharge, QuotationAdditionalCharge.
  - Chỉ migrate các dữ liệu có Quotation được tạo từ ngày 01/01/2025.
  - Drop các bảng cũ sau khi migrate thành công. (drop bảng ở db, code tạm thời để lại)



## Backlog

1. Keycloak SSO cho DataTP CRM.
 - Ngiên cứu, tích hợp vào app DataTP CRM, discuss với Đạt Lương.

2. Thông báo những giá mới được update/đang promote.
  - Theo dõi lịch sử search giá, gửi báo giá theo các tuyến của saleman, theo dõi lịch sử cập nhật, chỉnh sửa giá của pricing.
    Từ đó, gửi lại thông tin giá cho salesman (giá tăng, giá giảm so với giá gần nhất, người cập nhật, số lượng cập nhật, chi tiết một số giá mẫu, ...)
  - Ở màn hình tìm kiếm giá:
    - Từng line giá nên có icon theo chiều hướng lên/ xuống để thể hiện giá đó đang tăng hay giảm so với lần cập nhật trước hoặc trung bình cộng của kỳ trước (theo rule setup).
    - Viết cron cập nhật vào 8h hàng ngày.
      - Tạo thêm trường ở bảng giá FCL để thể hiện giá tăng/ giảm (%) so với lần cập nhật trước.
      - Cron sẽ chạy vào 8h hàng ngày, check các giá FCL mới nhất, nếu giá > giá cũ thì update field trên.

  - Tạo thêm một màn hình tổng quan về việc cập nhật giá theo các tuyến của pricing (tuyến nào, giá nào, ngày cập nhật, ai cập nhật, ...)
    để sales có thể theo dõi các giá mới cập nhật trên phần mềm.

6. Nghiên cứu chuyển qua code java trên VS Code.

7. Xử lý dữ liệu partners lost cho HCM

- Dữ liệu Tâm gửi dạng excel, gồm các khách hàng đã lâu ko phát sinh booking.
  Y/c dev kiểm tra/ check lại với hệ thống, nếu không phát sinh booking trong 1 năm gần đây => Import vào CRM.

- #sale_dashboard Enhance báo cáo Saleman Activities Tracker - Mrs. Minh Sales HPH
  - Cố định row header và cột đầu tiên.
  - Kiểm tra lại chỉ số Khách hàng mới (Bao gồm khách hàng được tạo mới hoặc được phân quyền - add saleman từ CS cũ).

- Review toàn bộ query trên module CRM (chuẩn bị cho việc tách database)

- Cập nhật thông tin các trường industry_sector (từ excel), date_created, date_modified (từ BFSOne) cho partner.
   -> Cập nhật xong, viết cron để sync lại với hệ thống bee_legacy (bảo Đàn làm)

