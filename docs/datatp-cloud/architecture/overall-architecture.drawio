<mxfile host="65bd71144e">
    <diagram name="Page-1" id="rvecp-LfN1xWRMMQkkwK">
        <mxGraphModel dx="890" dy="606" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-68" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="41" y="160" width="509" height="190" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-72" value="Developers" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="928" y="479" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-2" value="Propose Beelogistics Cloud" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=18;" parent="1" vertex="1">
                    <mxGeometry x="39" y="1" width="250" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-3" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="40" y="380" width="1540" height="2110" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-4" value="&lt;b style=&quot;&quot;&gt;IP Gateway&lt;/b&gt;&lt;span style=&quot;&quot;&gt;(Open Port 80, 443, 22)&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="50" y="396" width="1520" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-5" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="50" y="440" width="820" height="1530" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-11" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;dashed=1;dashPattern=8 8;" parent="1" vertex="1">
                    <mxGeometry x="50" y="1994" width="820" height="139" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-16" value="VM Machines - Optional" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="58" y="1997" width="160" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-17" value="&lt;b&gt;k8S&lt;/b&gt;(container pod management)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="65.5" y="1945" width="219" height="16" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-21" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="70" y="1840" width="780" height="92" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-23" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="50" y="2164" width="1520" height="276" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-24" value="Physical Servers" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="55" y="2162" width="160" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-25" value="" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;dashPattern=8 8;" parent="1" vertex="1">
                    <mxGeometry x="590" y="2205" width="566" height="75" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-26" value="Hosting Provider" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;dashPattern=8 8;" parent="1" vertex="1">
                    <mxGeometry x="1170" y="2205" width="388" height="165" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-36" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.layered_rect;dx=10;outlineConnect=0;whiteSpace=wrap;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
                    <mxGeometry x="70" y="1302" width="240" height="288" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-37" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.layered_rect;dx=10;outlineConnect=0;whiteSpace=wrap;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="331" y="1301" width="259" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-38" value="Master/Slave DB(Postgres, Mysql, MS SQL Server)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="78" y="1308" width="210" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-39" value="Distributed Temp DB" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="332" y="1301" width="235" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-40" value="Volume Storage(Files, Backup)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="72" y="1838" width="218" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-41" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.layered_rect;dx=10;outlineConnect=0;whiteSpace=wrap;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="60" y="890" width="530" height="400" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-42" value="Stateless MSA - Micro Service Architecture - Pod" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="74" y="895" width="291" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-43" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.layered_rect;dx=10;outlineConnect=0;whiteSpace=wrap;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="60" y="500" width="790" height="370" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-44" value="Stateless Application Sevice Pod" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="68" y="502" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-48" value="&lt;b&gt;Load Balancer Nginx Http Gateway&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="60" y="450" width="790" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-49" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="72" y="929" width="188" height="340" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-50" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="271" y="930" width="299" height="340" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-51" value="Developed MSA" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="88" y="930" width="282" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-52" value="Open Source, Commercial MSA" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="282" y="930" width="301" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-65" value="Check And Authorize Http Request - Keycloak SSO Service" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="76" y="540" width="754" height="31" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-72" value="" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="50" y="212" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-69" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="kl0TBJwefHdGWaGlJi7r-73" target="kl0TBJwefHdGWaGlJi7r-72" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-73" value="Webui&lt;div&gt;(DataTP, CMS, TMS, OKR/KPI, Documents)&lt;/div&gt;" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="93" y="190" width="203" height="108" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-76" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.164;exitY=0.012;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="167" y="374" as="sourcePoint"/>
                        <mxPoint x="167" y="300" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-78" value="DataTP Core" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="88" y="1360" width="82" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-79" value="CRM" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="81" y="1419" width="89" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-81" value="TMS" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="191" y="1420" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-82" value="CMS" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="83" y="1483" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-86" value="&lt;mxGraphModel&gt;&lt;root&gt;&lt;mxCell id=&quot;0&quot;/&gt;&lt;mxCell id=&quot;1&quot; parent=&quot;0&quot;/&gt;&lt;mxCell id=&quot;2&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&lt;mxGeometry x=&quot;74&quot; y=&quot;580&quot; width=&quot;406&quot; height=&quot;270&quot; as=&quot;geometry&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=&quot;3&quot; value=&quot;&amp;amp;nbsp;&amp;lt;span style=&amp;quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-wrap-mode: nowrap;&amp;quot;&amp;gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%2360a917%3BstrokeColor%3D%232D7600%3BfontColor%3D%23ffffff%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2282%22%20y%3D%22552%22%20width%3D%2223%22%20height%3D%229%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&amp;lt;/span&amp;gt;&amp;lt;span style=&amp;quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-wrap-mode: nowrap;&amp;quot;&amp;gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%2360a917%3BstrokeColor%3D%232D7600%3BfontColor%3D%23ffffff%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2282%22%20y%3D%22552%22%20width%3D%2223%22%20height%3D%229%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E" style="rounded=0;whiteSpace=wrap;html=1;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" parent="1" vertex="1">
                    <mxGeometry x="74" y="580" width="406" height="270" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-87" value="&amp;nbsp;&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%2360a917%3BstrokeColor%3D%232D7600%3BfontColor%3D%23ffffff%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2282%22%20y%3D%22552%22%20width%3D%2223%22%20height%3D%229%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%2360a917%3BstrokeColor%3D%232D7600%3BfontColor%3D%23ffffff%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2282%22%20y%3D%22552%22%20width%3D%2223%22%20height%3D%229%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &lt;/span&gt;DataTP Core" style="rounded=0;whiteSpace=wrap;html=1;align=left;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="80" y="614" width="190" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-92" value="Developed Apps" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="80" y="580" width="122" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-93" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="490" y="580" width="340" height="270" as="geometry"/>
                </mxCell>
                <mxCell id="kl0TBJwefHdGWaGlJi7r-95" value="Open Source, Commercial Apps" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="492" y="580" width="228" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-6" value="" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="190" y="1483" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-13" value="Redis" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="351" y="1331" width="89" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-14" value="RabbitMQ" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="460" y="1331" width="90" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-15" value="Kafka" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="351" y="1369" width="89" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-44" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="55" y="2390" width="1505" height="33" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-45" value="S3" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="59" y="2393" width="241.5" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-50" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="900" y="1641" width="669" height="247" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-51" value="Bee Legacy Software" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="908" y="1640" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-52" value="&amp;nbsp; CRM" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="910" y="1675" width="140" height="48" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-53" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="907" y="1785" width="283" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-54" value="MS SQL DB Server" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fillColor=#ffff88;strokeColor=#36393d;" parent="1" vertex="1">
                    <mxGeometry x="915" y="1795" width="135" height="73" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-55" value="MS SQL DB Server" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fillColor=#ffff88;strokeColor=#36393d;" parent="1" vertex="1">
                    <mxGeometry x="1060" y="1795" width="125" height="73" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-56" value="&amp;nbsp; HRP" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="910" y="1729" width="140" height="42" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-60" value="WebUI For Monitoring" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="1060" y="1675" width="130" height="98" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-79" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="900" y="1256" width="670" height="174" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-80" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="900" y="1458" width="668" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-92" value="Bee DAD" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="907" y="1257" width="150" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-98" value="Bee Others" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="907" y="1455" width="150" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-106" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="899" y="1899.5" width="669" height="248.5" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-107" value="&lt;b&gt;IT Development Env&lt;/b&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="907" y="1908" width="219" height="16" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-108" value="VM Windows" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="912.5" y="1957.5" width="132.5" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-109" value="VM Debian" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="1066.5" y="1954.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-110" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="910" y="2052.5" width="285" height="78.5" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-112" value="&lt;b&gt;k8S&lt;/b&gt;(container pod management)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="915" y="2059.5" width="219" height="16" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-124" value="VM Windows" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="908" y="1294.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-125" value="VM Debian" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="1056" y="1291.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-136" value="VM Windows" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="910" y="1494.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="4bm8_mLqzeammLvxw9Vd-137" value="VM Debian" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="1059" y="1493.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="t6FIC467CIvoHKia3Bv4-1" value="" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="192" y="1362" width="90" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="t6FIC467CIvoHKia3Bv4-2" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.layered_rect;dx=10;outlineConnect=0;whiteSpace=wrap;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="330" y="1430" width="260" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="t6FIC467CIvoHKia3Bv4-3" value="Distributed NoSQL DB" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="335" y="1431" width="235" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="t6FIC467CIvoHKia3Bv4-4" value="MongoDB" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="337" y="1460" width="95" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="t6FIC467CIvoHKia3Bv4-5" value="Metric DB(Prometheus, InfluxDB)" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="442" y="1460" width="110" height="53" as="geometry"/>
                </mxCell>
                <mxCell id="5oiLaY24LJRY3bxMea03-3" value="BFS One" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="363" y="190" width="168" height="102.5" as="geometry"/>
                </mxCell>
                <mxCell id="5oiLaY24LJRY3bxMea03-4" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.164;exitY=0.012;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="420" y="374" as="sourcePoint"/>
                        <mxPoint x="420" y="300" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5oiLaY24LJRY3bxMea03-5" value="" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="317" y="210" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9W52h4gfB-nIbCdkSlOG-7" value="VM K8S Node" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="72" y="2029" width="218" height="38" as="geometry"/>
                </mxCell>
                <mxCell id="9W52h4gfB-nIbCdkSlOG-11" value="VM K8S Node" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="336" y="2029" width="218" height="38" as="geometry"/>
                </mxCell>
                <mxCell id="9W52h4gfB-nIbCdkSlOG-13" value="VM Linux" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="596" y="2029" width="218" height="38" as="geometry"/>
                </mxCell>
                <mxCell id="9W52h4gfB-nIbCdkSlOG-15" value="VM K8S Node" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="74" y="2079" width="218" height="38" as="geometry"/>
                </mxCell>
                <mxCell id="9W52h4gfB-nIbCdkSlOG-17" value="VM K8S Node" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="336" y="2079" width="218" height="38" as="geometry"/>
                </mxCell>
                <mxCell id="9W52h4gfB-nIbCdkSlOG-19" value="VM Window" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="596" y="2079" width="218" height="38" as="geometry"/>
                </mxCell>
                <mxCell id="9W52h4gfB-nIbCdkSlOG-23" value="" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="74" y="1863" width="120" height="55" as="geometry"/>
                </mxCell>
                <mxCell id="9W52h4gfB-nIbCdkSlOG-24" value="" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="209" y="1863" width="120" height="55" as="geometry"/>
                </mxCell>
                <mxCell id="9W52h4gfB-nIbCdkSlOG-25" value="" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="357.5" y="1863" width="120" height="55" as="geometry"/>
                </mxCell>
                <mxCell id="9W52h4gfB-nIbCdkSlOG-26" value="" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="512" y="1863" width="120" height="55" as="geometry"/>
                </mxCell>
                <mxCell id="9W52h4gfB-nIbCdkSlOG-27" value="" style="ellipse;shape=cloud;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="680" y="1863" width="120" height="55" as="geometry"/>
                </mxCell>
                <mxCell id="3IGM_9iolHENLKWafwxM-11" value="Prometheus:&lt;div&gt;- Metric Scraper&lt;/div&gt;&lt;div style=&quot;&quot;&gt;- Alerter&lt;/div&gt;&lt;div style=&quot;&quot;&gt;- SNMP Exporter&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="423" y="962" width="106" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3IGM_9iolHENLKWafwxM-12" value="Appache Tika IE:&lt;div&gt;- PDF To Text&lt;/div&gt;&lt;div style=&quot;&quot;&gt;- XLSX To Text&lt;/div&gt;&lt;div style=&quot;&quot;&gt;- XDoc To Text&lt;/div&gt;&lt;div style=&quot;&quot;&gt;- PPT To Text&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="286" y="960" width="124" height="78.5" as="geometry"/>
                </mxCell>
                <mxCell id="3IGM_9iolHENLKWafwxM-13" value="OCR Service IE:&lt;div&gt;- Image To Text&lt;/div&gt;&lt;div style=&quot;&quot;&gt;- PDF To Text&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="286" y="1052" width="124" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3IGM_9iolHENLKWafwxM-14" value="&amp;nbsp;&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%2360a917%3BstrokeColor%3D%232D7600%3BfontColor%3D%23ffffff%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2282%22%20y%3D%22729%22%20width%3D%228%22%20height%3D%229%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;&amp;nbsp;&amp;nbsp; &amp;nbsp;Document IE:&lt;div&gt;- Xlsx&lt;/div&gt;&lt;div style=&quot;&quot;&gt;- Pdf&lt;/div&gt;&lt;div style=&quot;&quot;&gt;- VN Invoice&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="89" y="960" width="120" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="3IGM_9iolHENLKWafwxM-15" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#60a917;strokeColor=#2D7600;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="93" y="969" width="8" height="9" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-1" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.layered_rect;dx=10;outlineConnect=0;whiteSpace=wrap;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="73" y="1610" width="387" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-2" value="Data Warehouse" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="77" y="1610" width="113" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-5" value="..." style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="459.75" y="1369" width="90.5" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-6" value="..." style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="339" y="1521" width="211" height="21" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-9" value="Data Lake&lt;div&gt;- Text (json, csv...)&lt;/div&gt;&lt;div&gt;- Document(pdf, word...)&lt;/div&gt;&lt;div&gt;- SQL(HDFS + Hive)&lt;/div&gt;&lt;div&gt;- SQL(RDBM)&lt;/div&gt;&lt;div&gt;- NoSQL&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;align=left;" parent="1" vertex="1">
                    <mxGeometry x="88" y="1644.5" width="134" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-10" value="Data Warehouse:&lt;div&gt;- SQL(HDFS + Hive)&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;- NoSQL(Mongo DB)&lt;/span&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;align=left;" parent="1" vertex="1">
                    <mxGeometry x="314" y="1651" width="126" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-13" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.layered_rect;dx=10;outlineConnect=0;whiteSpace=wrap;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
                    <mxGeometry x="600" y="890" width="240" height="700" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-14" value="Data Warehouse Stateless MSA" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="602" y="894" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-24" value="Ingestion:&lt;div&gt;- Appache Nefi&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="608" y="945" width="108" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-25" value="Ingestion:&lt;div&gt;- Airbyte&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="723" y="945" width="99" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-26" value="Data Processing:&lt;div&gt;- Spark&lt;/div&gt;&lt;div&gt;- PySpark&lt;/div&gt;&lt;div&gt;- Hive(Sql)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="606" y="1016.5" width="218" height="83.5" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-29" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.layered_rect;dx=10;outlineConnect=0;whiteSpace=wrap;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
                    <mxGeometry x="486.5" y="1610" width="363.5" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-31" value="Full Text Search DB" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="498.5" y="1671" width="141.5" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-32" value="AI Model DB&lt;div&gt;(Chat GPT, Deepseek R1, Lama 4...)&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="680.5" y="1669" width="132.5" height="101" as="geometry"/>
                </mxCell>
                <mxCell id="IVli2XANMyWvMiEgoGCf-33" value="Index DB" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="492" y="1612" width="130.5" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="NbKdF0J2RX0kU1WqZspa-1" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="225" y="1736" as="sourcePoint"/>
                        <mxPoint x="311" y="1735" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="NbKdF0J2RX0kU1WqZspa-2" value="- Hive SQL&lt;div&gt;- Spark QL&lt;/div&gt;&lt;div&gt;- Python&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="231" y="1673" width="68" height="51" as="geometry"/>
                </mxCell>
                <mxCell id="QAmzusSdt7zl8eFKwtmm-10" value="Debian + Promox + CEPH + Mettric" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="602" y="2235" width="171" height="35" as="geometry"/>
                </mxCell>
                <mxCell id="QAmzusSdt7zl8eFKwtmm-14" value="" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;dashPattern=8 8;" parent="1" vertex="1">
                    <mxGeometry x="58" y="2204" width="518" height="166" as="geometry"/>
                </mxCell>
                <mxCell id="QAmzusSdt7zl8eFKwtmm-15" value="Debian + K8s + CEPH + Metric" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="66" y="2278" width="154" height="43" as="geometry"/>
                </mxCell>
                <mxCell id="QAmzusSdt7zl8eFKwtmm-29" value="VM Provider(Promox, VMWare...) - Optional" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="595" y="2203" width="265" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="QAmzusSdt7zl8eFKwtmm-33" value="Navive K8s Provider - Optional" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="63" y="2202" width="315" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="4bm8_mLqzeammLvxw9Vd-79" target="4bm8_mLqzeammLvxw9Vd-79" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-6" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="900" y="1060" width="669" height="174" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-7" value="Bee DAD" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="908" y="1061" width="150" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-8" value="VM Windows" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="909" y="1098.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-9" value="VM Debian" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="1057" y="1095.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="Rs-QSRFqiNmesrRnQxHP-6" target="Rs-QSRFqiNmesrRnQxHP-6" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-13" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="902" y="441" width="668" height="174" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-14" value="Bee Corp" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="909" y="442" width="150" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-15" value="VM Windows" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="910" y="479.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-16" value="VM Debian" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="1058" y="476.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="Rs-QSRFqiNmesrRnQxHP-13" target="Rs-QSRFqiNmesrRnQxHP-13" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-27" value="Developers" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="926" y="691" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-28" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="900" y="653" width="668" height="174" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-29" value="Bee HPH" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="907" y="654" width="150" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-30" value="VM Windows" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="908" y="691.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-31" value="VM Debian" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="1056" y="688.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="Rs-QSRFqiNmesrRnQxHP-28" target="Rs-QSRFqiNmesrRnQxHP-28" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-35" value="Developers" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="927" y="902" width="220" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-36" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="901" y="864" width="668" height="174" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-37" value="Bee HAN" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="908" y="865" width="150" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-38" value="VM Windows" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="909" y="902.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-39" value="VM Debian" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="1057" y="899.5" width="125" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-42" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="Rs-QSRFqiNmesrRnQxHP-36" target="Rs-QSRFqiNmesrRnQxHP-36" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-45" value="Debian + K8s + CEPH + Metric" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="234" y="2279" width="154" height="43" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-47" value="Debian + K8s + CEPH + Metric" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="409" y="2278" width="154" height="43" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-51" value="Debian + Promox + CEPH + Mettric" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="788" y="2236" width="171" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-53" value="Debian + Promox + CEPH + Mettric" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="971" y="2236" width="171" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-56" value="" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;dashPattern=8 8;" parent="1" vertex="1">
                    <mxGeometry x="589" y="2288" width="566" height="85" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-57" value="Debian + Kubevirt + CEPH + Mettric" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="598" y="2318" width="171" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-58" value="VM Provider(K8s KubeVirt...) - Optional" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="594" y="2286" width="265" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-62" value="Debian + Kubevirt + CEPH + Mettric" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="784" y="2317" width="171" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-64" value="Debian + Kubevirt + CEPH + Mettric" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="970" y="2317" width="171" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-70" value="Users" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="44" y="160" width="189" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-71" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="583" y="160" width="327" height="190" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-72" value="IOT" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="587" y="160" width="50" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-73" value="WIFI" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="593" y="191" width="147" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-75" value="Router + Switch" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="756" y="191" width="144" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-76" value="Office Camera" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="594" y="248" width="146" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-77" value="Office Camera" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="758" y="247" width="142" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-78" value="Truck Camera + GPS" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="1" vertex="1">
                    <mxGeometry x="596" y="299" width="146" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-79" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="935" y="160" width="355" height="190" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-80" value="Amin" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="939" y="163" width="141" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-81" value="SSH, RDP" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="990" y="200" width="130" height="102.5" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-82" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.164;exitY=0.012;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1047" y="373" as="sourcePoint"/>
                        <mxPoint x="1047" y="299" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-83" value="" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
                    <mxGeometry x="944" y="220" width="30" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-87" value="SSH, RDP" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
                    <mxGeometry x="1141" y="200" width="130" height="102.5" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-88" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.164;exitY=0.012;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1198" y="372" as="sourcePoint"/>
                        <mxPoint x="1198" y="298" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-89" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.164;exitY=0.012;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="760" y="375" as="sourcePoint"/>
                        <mxPoint x="760" y="337" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-113" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffff88;strokeColor=#36393d;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="230" y="621" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-114" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;fontColor=#ffffff;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="250" y="621" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-115" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="230" y="636" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-116" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="250" y="636" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-158" value="&amp;nbsp;&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%2360a917%3BstrokeColor%3D%232D7600%3BfontColor%3D%23ffffff%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2282%22%20y%3D%22552%22%20width%3D%2223%22%20height%3D%229%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%2360a917%3BstrokeColor%3D%232D7600%3BfontColor%3D%23ffffff%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2282%22%20y%3D%22552%22%20width%3D%2223%22%20height%3D%229%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &lt;/span&gt;DataTP CRM" style="rounded=0;whiteSpace=wrap;html=1;align=left;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="80" y="659" width="190" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-159" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffff88;strokeColor=#36393d;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="229" y="665" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-160" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;fontColor=#ffffff;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="249" y="665" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-161" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="229" y="680" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-162" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="249" y="680" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-168" value="&amp;nbsp;&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%2360a917%3BstrokeColor%3D%232D7600%3BfontColor%3D%23ffffff%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2282%22%20y%3D%22552%22%20width%3D%2223%22%20height%3D%229%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%2360a917%3BstrokeColor%3D%232D7600%3BfontColor%3D%23ffffff%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2282%22%20y%3D%22552%22%20width%3D%2223%22%20height%3D%229%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &lt;/span&gt;DataTP TMS" style="rounded=0;whiteSpace=wrap;html=1;align=left;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="80" y="704" width="190" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-169" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffff88;strokeColor=#36393d;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="229" y="711" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-170" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;fontColor=#ffffff;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="249" y="711" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-171" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="229" y="726" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-172" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="249" y="726" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-173" value="&amp;nbsp;&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%2360a917%3BstrokeColor%3D%232D7600%3BfontColor%3D%23ffffff%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2282%22%20y%3D%22552%22%20width%3D%2223%22%20height%3D%229%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot%3E%3C%2FmxGraphModel%3E&lt;/span&gt;&lt;span style=&quot;color: rgba(0, 0, 0, 0); font-family: monospace; font-size: 0px; text-wrap-mode: nowrap;&quot;&gt;%3CmxGraphModel%3E%3Croot%3E%3CmxCell%20id%3D%220%22%2F%3E%3CmxCell%20id%3D%221%22%20parent%3D%220%22%2F%3E%3CmxCell%20id%3D%222%22%20value%3D%22%22%20style%3D%22rounded%3D0%3BwhiteSpace%3Dwrap%3Bhtml%3D1%3BfillColor%3D%2360a917%3BstrokeColor%3D%232D7600%3BfontColor%3D%23ffffff%3B%22%20vertex%3D%221%22%20parent%3D%221%22%3E%3CmxGeometry%20x%3D%2282%22%20y%3D%22552%22%20width%3D%2223%22%20height%3D%229%22%20as%3D%22geometry%22%2F%3E%3C%2FmxCell%3E%3C%2Froot&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &lt;/span&gt;DataTP OKR/KPI" style="rounded=0;whiteSpace=wrap;html=1;align=left;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="80" y="748" width="190" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-174" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffff88;strokeColor=#36393d;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="229" y="755" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-175" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;fontColor=#ffffff;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="249" y="755" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-176" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="229" y="770" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-177" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="249" y="770" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-185" value="Document + IE + CMS&lt;div&gt;(Base On Strapi)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="280" y="615" width="190" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-186" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffff88;strokeColor=#36393d;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="430" y="622" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-187" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;fontColor=#ffffff;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="450" y="622" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-188" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="430" y="637" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-189" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="450" y="637" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-190" value="Moodle(LMS)" style="rounded=0;whiteSpace=wrap;html=1;align=left;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="496" y="615" width="154" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-191" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="610" y="621" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-192" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fa6800;strokeColor=#C73500;fontColor=#000000;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="630" y="621" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-193" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="610" y="636" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-194" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="630" y="636" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-195" value="Gitlab" style="rounded=0;whiteSpace=wrap;html=1;align=left;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="657" y="614" width="163" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-196" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="779" y="620" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-197" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffff88;strokeColor=#36393d;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="799" y="620" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-198" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="779" y="635" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-199" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="799" y="635" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-200" value="Grafana" style="rounded=0;whiteSpace=wrap;html=1;align=left;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="496" y="662" width="154" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-201" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="610" y="668" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-202" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffff88;strokeColor=#36393d;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="630" y="668" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-203" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="610" y="683" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-204" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="630" y="683" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-205" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="782" y="543" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-206" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffff88;strokeColor=#36393d;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="802" y="543" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-207" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="782" y="558" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-208" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="802" y="558" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-209" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="812" y="453" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-210" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffff88;strokeColor=#36393d;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="832" y="453" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-211" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="812" y="468" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-212" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="832" y="468" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-213" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="40.25" y="51" width="236.75" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-215" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="49" y="88" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-216" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffff88;strokeColor=#36393d;" parent="1" vertex="1">
                    <mxGeometry x="69" y="88" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-217" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="49" y="103" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-218" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="69" y="103" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-219" value="- Chất Lượng Phần Mềm&lt;div&gt;- Triển Khai Thử Nghiệm&lt;/div&gt;&lt;div&gt;- Triển Khai Toàn Bộ Công Ty&lt;/div&gt;&lt;div&gt;- Phản Hồi Người Dùng&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="93" y="70" width="174" height="71" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-221" value="Tiêu Chí Cho IT" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="44" y="52" width="90" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-224" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
                    <mxGeometry x="303" y="50" width="267" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-225" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;strokeColor=#005700;fontColor=#ffffff;" parent="1" vertex="1">
                    <mxGeometry x="311.75" y="87" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-226" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffff88;strokeColor=#36393d;" parent="1" vertex="1">
                    <mxGeometry x="331.75" y="87" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-227" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="311.75" y="102" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-228" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="331.75" y="102" width="14" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-229" value="- Triển Khai&lt;div&gt;- Tuân Thủ&lt;/div&gt;&lt;div&gt;- Khai Thác&lt;/div&gt;&lt;div&gt;- ??&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="358.75" y="70" width="174" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="Rs-QSRFqiNmesrRnQxHP-230" value="Tiêu Chí Cho Chi Nhánh, CT Thành Viên" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
                    <mxGeometry x="306.75" y="51" width="263.25" height="20" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>