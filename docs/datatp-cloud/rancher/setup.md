# Setup Debian
```sh
sudo apt-get update -y
sudo apt-get upgrade -y
sudo apt-get install ufw -y
sudo apt-get install vim -y
sudo apt-get install curl -y
sudo apt-get install iptables -y

sysctl net.ipv4.ip_forward
iptables -P INPUT ACCEPT && iptables -P FORWARD ACCEPT && iptables -P OUTPUT ACCEPT && iptables -F
iptables -A INPUT -p tcp -m tcp --dport 6443 -j ACCEPT
``` 

Edit /etc/hosts and add
```sh
*************	k8s-node-001
*************	k8s-node-002
*************	k8s-node-003
```

Install Helm on master node only(optional)
```sh
sudo apt-get install gpg -y
sudo apt-get install apt-transport-https --yes
curl https://baltocdn.com/helm/signing.asc | gpg --dearmor | sudo tee /usr/share/keyrings/helm.gpg > /dev/null
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main" | sudo tee /etc/apt/sources.list.d/helm-stable-debian.list
sudo apt-get update
sudo apt-get install helm
```
# Setup RKE2

See https://ranchermanager.docs.rancher.com/how-to-guides/new-user-guides/kubernetes-cluster-setup/rke2-for-rancher

First, you must create the directory where the RKE2 config file is going to be placed:
```sh
mkdir -p /etc/rancher/rke2/
```

Next, create the RKE2 config file at /etc/rancher/rke2/config.yaml using the following example:
```sh
token: Bee@Cloud
#server: https://k8s-node-001:9345
tls-san:
  - beelogistics.cloud
  - beelogistics.com

#disable:
#- rke2-ingress-nginx
#- rke2-snapshot-controller
#- rke2-snapshot-controller-crd
#- rke2-snapshot-validation-webhook
```

Download rke2 script
```sh
mkdir -p /tmp/rke2 && cd /tmp/rke2
curl -sfL https://get.rke2.io > rke2.sh
chmod +x rke2.sh
```

Install and start rke2 server
```sh
INSTALL_RKE2_CHANNEL=stable INSTALL_RKE2_METHOD=tar ./rke2.sh
#INSTALL_RKE2_CHANNEL=stable INSTALL_RKE2_METHOD=tar INSTALL_RKE2_VERSION=v1.33.2+rke2r1 ./rke2.sh
systemctl enable rke2-server.service && systemctl start rke2-server.service
```

Install and start rke2 agent
```sh
INSTALL_RKE2_CHANNEL=stable INSTALL_RKE2_METHOD=tar ./rke2.sh
systemctl enable rke2-agent.service && systemctl start rke2-agent.service
```