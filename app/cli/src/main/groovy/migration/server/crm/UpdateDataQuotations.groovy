package migration.server.crm

import cloud.datatp.fforwarder.sales.common.CustomerChargeLogic
import cloud.datatp.fforwarder.sales.common.entity.QuotationAdditionalCharge
import cloud.datatp.fforwarder.sales.common.entity.QuotationCharge
import cloud.datatp.fforwarder.sales.quotation.SpecificQuotationLogic
import cloud.datatp.fforwarder.sales.quotation.charge.CustomerAirChargeLogic
import cloud.datatp.fforwarder.sales.quotation.charge.CustomerSeaChargeLogic
import cloud.datatp.fforwarder.sales.quotation.entity.QuotationAirTransportCharge
import cloud.datatp.fforwarder.sales.quotation.entity.QuotationSeaTransportCharge
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.common.ClientInfo
import net.datatp.module.company.CompanyLogic
import net.datatp.module.company.entity.Company
import net.datatp.module.data.db.SqlMapRecord
import net.datatp.module.data.db.SqlQueryManager
import net.datatp.module.data.db.SqlSelectView
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.util.ds.Collections
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import javax.sql.DataSource

class UpdateQuotation extends ServiceRunnableSet {
    private static final Logger log = LoggerFactory.getLogger(UpdateQuotation.class);
    private static String label = "------------UPDATE DATA QUOTATION--------------"

    public UpdateQuotation() {
        super(label);
        ServiceRunnable syncService = new ServiceRunnable(label) {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                ClientInfo client = scriptCtx.getClientInfo();
                Company company = (Company) scriptCtx.getCompany();
                SpecificQuotationLogic specificQuotationLogic = scriptCtx.getService(SpecificQuotationLogic.class);
                CustomerAirChargeLogic customerAirChargeLogic = scriptCtx.getService(CustomerAirChargeLogic.class);
                CustomerSeaChargeLogic customerSeaChargeLogic = scriptCtx.getService(CustomerSeaChargeLogic.class);
                CustomerChargeLogic customerChargeLogic = scriptCtx.getService(CustomerChargeLogic.class);
                CompanyLogic companyLogic = scriptCtx.getService(CompanyLogic.class);
                DataSource ds = specificQuotationLogic.getJdbcDataSource();
                SqlQueryManager sqlQueryManager = scriptCtx.getService(SqlQueryManager.class);

                // findBySpecificQuotationId

                final String dataDir = scriptCtx.getDataDir();
                String SCRIPT_DIR = dataDir + "/crm/sql";
                String fileName = "SelectQuotationThisYear.groovy";
                SqlQueryManager.QueryContext queryContext = sqlQueryManager.create(SCRIPT_DIR, fileName);
                Binding binding = new Binding();
                SqlSelectView view = queryContext.createSqlSelectView(ds, binding);
                List<SqlMapRecord> quo = view.renameColumWithJavaConvention().getSqlMapRecords();

                /*
                List<String> companyCodes = Arrays.asList("beehph", "beehcm", "beedad", "beehan", "bee");
                Map<String, Company> companyMap = new HashMap<>();
                for (String companyCode : companyCodes) {
                    Company company = companyLogic.getCompany(client, companyCode);
                    companyMap.put(companyCode, company);
                }
                 */

                for (SqlMapRecord record : quo) {
                    Long id = record.getLong("id");
                    List<QuotationSeaTransportCharge> quotationSeaTransportCharges = customerSeaChargeLogic.findBySpecificQuotationId(client, company, id);
                    if (Collections.isNotEmpty(quotationSeaTransportCharges)) {
                        for (QuotationSeaTransportCharge charge : quotationSeaTransportCharges) {
                            QuotationCharge quotationCharge = new QuotationCharge(charge);
                            quotationCharge.setSpecificQuotationId(id);
                            customerChargeLogic.saveQuotationCharge(client, company, quotationCharge);
                            List<QuotationAdditionalCharge> additionalCharges = QuotationAdditionalCharge.computeFromCharges(charge.getAdditionalCharges());
                            customerChargeLogic.saveAdditionalCharges(client, company, id, additionalCharges);
                        }
                        break;
                    }

                    List<QuotationAirTransportCharge> quotationAirTransportCharges = customerAirChargeLogic.findBySpecificQuotationId(client, company, id);
                    if (Collections.isNotEmpty(quotationAirTransportCharges)) {
                        for (QuotationAirTransportCharge charge : quotationAirTransportCharges) {
                            QuotationCharge quotationCharge = new QuotationCharge(charge);
                            quotationCharge.setSpecificQuotationId(id);
                            customerChargeLogic.saveQuotationCharge(client, company, quotationCharge);
                            List<QuotationAdditionalCharge> additionalCharges = QuotationAdditionalCharge.computeFromCharges(charge.getAdditionalCharges());
                            customerChargeLogic.saveAdditionalCharges(client, company, id, additionalCharges);
                        }
                        break;
                    };
                }
            }
        };
        addRunnable(syncService);
    }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-06-19");

UpdateQuotation updateQuotation = new UpdateQuotation();
updateQuotation.run(reporter, scriptCtx);
return "DONE!!!";