package migration.server.crm

import cloud.datatp.fforwarder.price.common.ProvinceNormalizer
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.common.ClientInfo
import net.datatp.module.company.entity.Company
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.module.hr.HRService
import net.datatp.module.hr.entity.Employee
import net.datatp.util.text.StringUtil
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class UpdateDataEmployees extends ServiceRunnableSet {
    private static final Logger log = LoggerFactory.getLogger(UpdateDataEmployees.class);
    private static String label = "------------UPDATE DATA EMPLOYEE--------------"

    public UpdateDataEmployees() {
        super(label);
        ServiceRunnable syncService = new ServiceRunnable(label) {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                ClientInfo client = scriptCtx.getClientInfo();
                Company company = (Company) scriptCtx.getCompany();
                HRService hRService = scriptCtx.getService(HRService.class);
                List<Employee> employees = hRService.findEmployees(client, company);
                for (Employee employee : employees) {
                    Set<String> variants = new LinkedHashSet<>();
                    String label = employee.getLabel();
                    if(StringUtil.isNotEmpty(label)) {
                        String labelRemoveVietnameseTone = ProvinceNormalizer.removeVietnameseTone(label).toLowerCase();
                        variants.add(labelRemoveVietnameseTone.replace(" ", ""));
                    };

                    String username = employee.getBfsoneUsername();
                    if(StringUtil.isNotEmpty(username))  {
                        variants.add(username.toLowerCase());
                    }
                    employee.setVariants(variants);
                    hRService.saveEmployee(client, company, employee);
                }
            }
        };
        addRunnable(syncService);
    }
}


ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-06-19");

UpdateDataEmployees updateEmployee = new UpdateDataEmployees();
updateEmployee.run(reporter, scriptCtx);
return "DONE!!!";