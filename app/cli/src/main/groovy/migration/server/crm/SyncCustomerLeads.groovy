package migration.server.crm

import cloud.datatp.fforwarder.price.common.ProvinceNormalizer
import cloud.datatp.fforwarder.sales.partner.CustomerLeadsLogic
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.common.ClientInfo
import net.datatp.module.company.entity.Company
import net.datatp.module.company.entity.CompanyConfig
import net.datatp.module.data.db.ExternalDataSourceManager
import net.datatp.module.data.db.SqlMapRecord
import net.datatp.module.data.db.SqlQueryManager
import net.datatp.module.data.db.SqlSelectView
import net.datatp.module.data.xlsx.XSLXToMapObjectParser
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.module.hr.entity.Employee
import net.datatp.module.resource.location.CountryLogic
import net.datatp.module.resource.location.StateLogic
import net.datatp.module.resource.location.entity.Country
import net.datatp.module.resource.location.entity.State
import net.datatp.util.dataformat.DataSerializer
import net.datatp.util.ds.Collections
import net.datatp.util.ds.MapObject
import net.datatp.util.io.IOUtil
import net.datatp.util.text.StringUtil
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource

import javax.sql.DataSource

class SyncBFSOneCustomerLeads extends ServiceRunnableSet {
    private static final Logger log = LoggerFactory.getLogger(UpdateQuotation.class);
    private static String label = "------------SYNC BFSONE CUSTOMER LEAD--------------"

    public SyncBFSOneCustomerLeads() {
        super(label);
        ServiceRunnable syncService = new ServiceRunnable(label) {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                ClientInfo client = scriptCtx.getClientInfo();
                CustomerLeadsLogic customerLeadLogic = scriptCtx.getService(CustomerLeadsLogic.class);

                List<String> updatedLeads = new ArrayList<>();
                List<String> createdLeads = new ArrayList<>();

                List<String> companyCodes = Arrays.asList("beehph", "beehcm", "beedad", "beehan", "bee");
                Map<String, Company> companyMap = new HashMap<>();
                for (String companyCode : companyCodes) {
                    Company company = customerLeadLogic.getCompanyLogic().getCompany(client, companyCode);
                    if (company != null) companyMap.put(companyCode, company);
                }

                SqlQueryManager sqlQueryManager = scriptCtx.getService(SqlQueryManager.class);
                Company beehph = customerLeadLogic.getCompanyLogic().getCompany(client, "beehph");
                CompanyConfig companyConfig = customerLeadLogic.getCompanyConfigLogic()
                        .getCompanyConfigByCompanyId(client, beehph.getId());
                ExternalDataSourceManager.DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds",
                        ExternalDataSourceManager.DataSourceParams.class);
                DataSource ds = customerLeadLogic.getDataSourceManager().getDataSource(beehph, dsPrams);

                final String dataDir = scriptCtx.getDataDir();
                String SCRIPT_DIR = dataDir + "/crm/sql";
                String fileName = "SyncBFSOneCustomerLeadsSql.groovy";
                SqlQueryManager.QueryContext queryContext = sqlQueryManager.create(SCRIPT_DIR, fileName);
                Binding binding = new Binding();
                SqlSelectView view = queryContext.createSqlSelectView(ds, binding);
                List<SqlMapRecord> leads = view.renameColumWithJavaConvention().getSqlMapRecords();

                Map<String, List<SqlMapRecord>> groupedByUsername = new HashMap<>();
                for (SqlMapRecord rec : leads) {
                    String username = rec.getString("username", "").trim();
                    groupedByUsername.putIfAbsent(username, new ArrayList<>());
                    groupedByUsername.get(username).add(rec);
                }

                for (String username : groupedByUsername.keySet()) {
                    Employee employee = null;
                    Company currentCompany = null;
                    for (Company company : companyMap.values()) {
                        employee = customerLeadLogic.getEmployeeReadLogic().getEmployeeByBFSOneUsername(client, company, username);
                        if (employee != null) {
                            currentCompany = company;
                            break;
                        }
                    }

                    if (employee == null) {
                        log.info("---- Not exists Employee with BFSOne Username: = {}", username);
                    } else {
                        for (SqlMapRecord rec : groupedByUsername.get(username)) {
                            try {
                                CustomerLeads lead = new CustomerLeads(rec);
                                lead.withSaleman(employee);
                                CustomerLeads leadInDb = customerLeadLogic.getCustomerLeadByCode(client, currentCompany, lead.getCode());
                                if (leadInDb != null) {
                                    leadInDb.computeFromObject(rec);
                                    customerLeadLogic.saveCustomerLead(client, beehph, leadInDb);
                                    updatedLeads.add(leadInDb.getCode());
                                } else {
                                    customerLeadLogic.saveCustomerLead(client, beehph, lead);
                                    createdLeads.add(lead.getCode());
                                }
                            } catch (Exception e) {
                                log.error("Fail to save Customer Lead: {}", rec, e);
                            }
                        }
                    }
                }
                log.info("===== CREATED CUSTOMER LEADS ({}) =====", createdLeads.size());
                log.info("===== UPDATED CUSTOMER LEADS ({}) =====", updatedLeads.size());

            }
        };
        addRunnable(syncService);
    }
}

class ImportCustomerLeads extends ServiceRunnableSet {
    private static final Logger log = LoggerFactory.getLogger(ImportCustomerLeads.class);
    private static String label = "------------IMPORT CUSTOMER LEAD FROM EXCEL--------------"

    public ImportCustomerLeads() {
        super(label);
        ServiceRunnable syncService = new ServiceRunnable(label) {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                CustomerLeadsLogic customerLeadLogic = scriptCtx.getService(CustomerLeadsLogic.class);
                ClientInfo client = scriptCtx.getClientInfo();
                Company company = (Company) scriptCtx.getCompany();

                Map<String, Company> companyMap = new HashMap<>();
                Company beehan = customerLeadLogic.getCompanyLogic().getCompany(client, "beehan");
                companyMap.put("HAN", beehan);
                companyMap.put("HCM", company);

                final String dataDir = scriptCtx.getDataDir();
                String res = "file:" + dataDir + "/crm/analysis_data.xlsx";
                InputStream is = IOUtil.loadResource(res);
                final byte[] data = IOUtil.getStreamContentAsBytes(is);
                XSLXToMapObjectParser parser = new XSLXToMapObjectParser(data);
                parser.parse(parser.getFirstSheetName());
                List<MapObject> records = parser.getRows();
                Map<String, List<MapObject>> groupedBySaleman = new HashMap<>();

                for (MapObject rec : records) {
                    String salemanSummary = rec.getString("allocate_code");
                    // split by , to get list of saleman
                    String[] salemanList = salemanSummary.split(",");
                    for (String saleman : salemanList) {
                        groupedBySaleman.putIfAbsent(saleman, new ArrayList<>());
                        groupedBySaleman.get(saleman).add(rec);
                    }
                }

                for (String saleman : groupedBySaleman.keySet()) {
                    List<Employee> employees = customerLeadLogic.getEmployeeReadLogic().findEmployeeByBFSOneUsername(client, saleman.trim());
                    Employee salemanEmp = null;
                    if (employees.size() == 0) {
                        log.info("---- Not exists Employee with BFSOne Username: = {}", saleman);
                        continue;
                    } else {
                        salemanEmp = employees.get(0);
                    }

                    if (salemanEmp != null) {
                        for (MapObject rec : groupedBySaleman.get(saleman)) {
                            try {
                                String partnerId = rec.getString("partner_id", "")
                                String lastTransactionId = rec.getString("last_transaction_id", "")
                                if (StringUtil.isNotEmpty(partnerId) && StringUtil.isNotEmpty(lastTransactionId)) {
                                    log.info("-Lead existing in BFSOne: {}", partnerId)
                                    continue;
                                }

                                if (StringUtil.isNotEmpty(partnerId) && StringUtil.isNotEmpty(lastTransactionId)) {
                                    log.info("-Lead existing in BFSOne, but not exist transaction: {}", partnerId)
                                }

                                String taxCode = rec.getString("tax_code", "");
                                taxCode = taxCode.replace("'", "");
                                List<CustomerLeads> leadInDb = customerLeadLogic.getLeadsRepo().findByTaxCode(taxCode);
                                if (leadInDb != null && leadInDb.size() > 0) {
                                    log.info("Lead existing in DB: {} - {} - saleman: {} - allocated to {}", leadInDb.get(0).getCode(), leadInDb.get(0).getName(), leadInDb.get(0).getSalemanLabel(), saleman)
                                    continue;
                                }

                                CustomerLeads lead = new CustomerLeads(rec);
                                lead.setDate(new Date());
                                lead.setTaxCode(taxCode);
                                lead.setName(rec.getString("company_name"));
                                lead.setCell(rec.getString("phone"));
                                lead.setAddress(rec.getString("address"));
                                lead.setLocalizedAddress(rec.getString("address"));
                                lead.setStatus(LeadStatus.NEW);
                                lead.setBfsonePartnerCode(rec.getString("partner_id", ""));
                                lead.setRouting(rec.getString("pol_pod_summary", ""))
                                StringJoiner joiner = new StringJoiner("\n");
                                String hsCode = rec.getString("hs_code", "")
                                if (StringUtil.isNotEmpty(hsCode)) {
                                    joiner.add("HS Code: " + hsCode);
                                }

                                joiner.add(rec.getString("desc_of_good", ""));
                                lead.setNote(joiner.toString());
                                lead.withSaleman(salemanEmp);
                                customerLeadLogic.saveCustomerLead(client, company, lead);
                            } catch (Exception e) {
                                log.error("Fail to save Customer Lead: {}", rec, e);
                            }
                        }
                    } else {
                        log.info("---- Not exists Employee with BFSOne Username: = {}", saleman);
                    }
                }

            }
        };
        addRunnable(syncService);
    }
}

class SyncPartnerLost extends ServiceRunnableSet {
    private static final Logger log = LoggerFactory.getLogger(SyncPartnerLost.class);
    private static String label = "------------SYNC PARTNER LOST--------------"

    public SyncPartnerLost() {
        super(label);
        ServiceRunnable syncService = new ServiceRunnable(label) {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                ClientInfo client = scriptCtx.getClientInfo();
                Company company = (Company) scriptCtx.getCompany();
                CustomerLeadsLogic customerLeadLogic = scriptCtx.getService(CustomerLeadsLogic.class);
                DataSource bfsoneReportDataSource = customerLeadLogic.getBfsoneReportDataSource();
                final String dataDir = scriptCtx.getDataDir();

                SqlQueryManager sqlQueryManager = scriptCtx.getService(SqlQueryManager.class);
                String SCRIPT_DIR = dataDir + "/crm/sql";
                String fileName = "SelectHouseBillByPartnerId.groovy";
                SqlQueryManager.QueryContext queryContext = sqlQueryManager.create(SCRIPT_DIR, fileName);
                MapSqlParameterSource parameter = new MapSqlParameterSource();
                Binding binding = new Binding();

                String res = "file:" + dataDir + "/crm/HCM_partner_lost.xlsx";
                InputStream is = IOUtil.loadResource(res);
                final byte[] data = IOUtil.getStreamContentAsBytes(is);
                XSLXToMapObjectParser parser = new XSLXToMapObjectParser(data);
                parser.parse(parser.getFirstSheetName());
                List<MapObject> recordExcel = parser.getRows();

                for (MapObject rec : recordExcel) {
                    String partnerId = rec.getString("PartnerID", "").trim();
                    parameter.addValue("customerCode", partnerId);

                    SqlSelectView view = queryContext.createSqlSelectView(bfsoneReportDataSource, binding, parameter);
                    List<SqlMapRecord> records = view.renameColumWithJavaConvention().getSqlMapRecords();
                    if (Collections.isEmpty(records)) {
                        log.info("No records found for Partner ID: {}", partnerId);
                        continue;
                    };
                    String lastTransactionId = records.get(0).getString("lastTransactionId", "");
                    String lastReportDate = records.get(0).getString("lastReportDate", "");
                    String nominatedRecordCount = records.get(0).getString("nominatedRecordCount", "");
                    String freehandRecordCount = records.get(0).getString("freehandRecordCount", "");
                    String totalProfit = records.get(0).getString("totalProfit", "");

                    String sales = rec.getString("SALES");
                    String allocateCode = sales.split(" - ")[1].trim();
                    rec.with("AllocateCode", allocateCode)
                            .with("LastTransactionId", lastTransactionId)
                            .with("LastReportDate", lastReportDate)
                            .with("NominatedRecordCount", nominatedRecordCount)
                            .with("FreehandRecordCount", freehandRecordCount)
                            .with("TotalProfit", totalProfit);
                }
                File outputFile = new File("person.json");
                DataSerializer.JSON.getObjectMapper().writeValue(outputFile, recordExcel);
                //DataSerializer.JSON.dump(recordExcel);
            }
        };
        addRunnable(syncService);
    }
}

//TODO: Dan - need to review
class ImportLostCustomerLeads extends ServiceRunnableSet {
    private static final Logger log = LoggerFactory.getLogger(ImportLostCustomerLeads.class);
    private static String label = "------------IMPORT LOST CUSTOMER LEAD FROM EXCEL--------------"

    public ImportLostCustomerLeads() {
        super(label);
        ServiceRunnable syncService = new ServiceRunnable(label) {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                CustomerLeadsLogic customerLeadLogic = scriptCtx.getService(CustomerLeadsLogic.class);
                ClientInfo client = scriptCtx.getClientInfo();
                ProvinceNormalizer provinceNormalizer = scriptCtx.getService(ProvinceNormalizer.class);
                Company beehcm = customerLeadLogic.getCompanyLogic().getCompany(client, "beehcm");
                CountryLogic countryLogic = scriptCtx.getService(CountryLogic.class);
                StateLogic stateLogic = scriptCtx.getService(StateLogic.class);
                Country vn = countryLogic.getCountry(client, "VN");
                List<State> stateList = stateLogic.findStatesInCountry(client, vn.getCode());

                provinceNormalizer.cacheProvince(stateList);

                final String dataDir = scriptCtx.getDataDir();
                String res = "file:" + dataDir + "/crm/partner_lost.xlsx";
                InputStream is = IOUtil.loadResource(res);
                final byte[] data = IOUtil.getStreamContentAsBytes(is);
                XSLXToMapObjectParser parser = new XSLXToMapObjectParser(data);
                parser.parse(parser.getFirstSheetName());
                List<MapObject> records = parser.getRows();

                for (MapObject rec : records) {
                    String taxCode = rec.getString("tax_code").trim();
                    String stateExcel = rec.getString("city_name", "").trim().toLowerCase()
                            .replace("-", "").replace("  ", " ");
                    if (taxCode.startsWith("'")) taxCode = taxCode.substring(1);
                    List<CustomerLeads> leadInDb = customerLeadLogic.findCustomerLeadByTaxCode(client, beehcm, taxCode);
                    if (Collections.isNotEmpty(leadInDb)) continue;
                    String salemanSummary = rec.getString("allocate_code");
                    List<Employee> employees = customerLeadLogic.getEmployeeReadLogic().findEmployeeByBFSOneUsername(client, salemanSummary.trim());
                    if (stateExcel == "hcm") stateExcel = "ho chi minh";
                    State state = provinceNormalizer.findStateProvince(stateExcel);

                    CustomerLeads lead = new CustomerLeads(rec);
                    lead.setDate(new Date());
                    lead.setTaxCode(taxCode);
                    lead.setCell(rec.getString("phone"));
                    lead.setLocalizedAddress(rec.getString("address"));
                    lead.setBfsonePartnerCode(rec.getString("partner_id", ""));
                    lead.setCountryId(vn.getId());
                    lead.setCountryLabel(vn.getLabel());
                    lead.setProvinceId(state.getId());
                    lead.setProvinceLabel(state.getLabel());
                    lead.withSaleman(employees);
                    customerLeadLogic.saveCustomerLead(client, beehcm, lead);
                }
            }
        };
        addRunnable(syncService);
    }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-06-19");

UpdateQuotation syncBFSOneCustomerLeads = new UpdateQuotation();
//syncBFSOneCustomerLeads.run(reporter, scriptCtx);

ImportCustomerLeads importCustomerLeads = new ImportCustomerLeads();
//importCustomerLeads.run(reporter, scriptCtx);

SyncPartnerLost syncPartnerLost = new SyncPartnerLost();
//syncPartnerLost.run(reporter, scriptCtx);

ImportLostCustomerLeads importLostCustomerLeads = new ImportLostCustomerLeads();
importLostCustomerLeads.run(reporter, scriptCtx);
return "DONE!!!";