package migration.server.crm


import org.slf4j.Logger
import org.slf4j.LoggerFactory

import cloud.datatp.fforwarder.sales.partner.PartnerReportLogic
import cloud.datatp.fforwarder.sales.partner.entity.SalemanKeyAccountReport
import cloud.datatp.fforwarder.sales.partner.entity.SalemanKeyAccountReport.ReportType
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.common.ClientInfo
import net.datatp.module.company.entity.Company
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.util.ds.Collections
import net.datatp.util.ds.MapObject

class UpdateKeyAccountReport extends ServiceRunnableSet {
  private static final Logger logger = LoggerFactory.getLogger(UpdateDataKeyAccountReport.class);
  private static String label = "------------UPDATE DATA KEY ACCOUNT REPORT--------------"

  public UpdateKeyAccountReport() {
    super(label);
    ServiceRunnable syncService = new ServiceRunnable(label) {
      @Override
      public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
        ClientInfo client = scriptCtx.getClientInfo();
        Company company = (Company) scriptCtx.getCompany();
        PartnerReportLogic logic = scriptCtx.getService(PartnerReportLogic.class);
        
        List<SalemanKeyAccountReport> reports = logic.findSalemanKeyAccountReports(client, company);
        for (SalemanKeyAccountReport report : reports) {
          MapObject volumePerformance = report.getVolumePerformance();
          if (report.getType() == ReportType.SALES) {
            List<MapObject> airImp = (ArrayList) volumePerformance.get("airImp");
            computeData(report, airImp)
            
            List<MapObject> airExp = (ArrayList) volumePerformance.get("airExp");
            computeData(report, airExp)

            List<MapObject> fclExp = (ArrayList) volumePerformance.get("fclExp");
            computeData(report, fclExp)
            
            List<MapObject> fclImp = (ArrayList) volumePerformance.get("fclImp");
            computeData(report, fclImp)
            
            List<MapObject> lclExp = (ArrayList) volumePerformance.get("lclExp");
            computeData(report, lclExp)
            
            List<MapObject> lclImp = (ArrayList) volumePerformance.get("lclImp");
            computeData(report, lclImp)
            
            List<MapObject> otherService = (ArrayList) volumePerformance.get("otherService");
            computeData(report, otherService)
          } else {
            List<MapObject> airService = (ArrayList) volumePerformance.get("airService");
            computeData(report, airService)
            
            List<MapObject> fclService = (ArrayList) volumePerformance.get("fclService");
            computeData(report, fclService)
            
            List<MapObject> otherService = (ArrayList) volumePerformance.get("otherService");
            computeData(report, otherService)
          }
          
          logic.saveSalemanKeyAccountReport(client, company, report);
        }
      }
      
      private void computeData(SalemanKeyAccountReport report, List<MapObject> records) {
        if (Collections.isEmpty(records)) return;
        double volume = report.getVolume() != null ? report.getVolume() : 0.0;
        double profit = report.getProfit() != null ? report.getProfit() : 0.0;
        double revenue = report.getRevenue() != null ? report.getRevenue() : 0.0;
        
        for (MapObject record : records) {
          volume += getDoubleValue(record, "volume");
          profit += getDoubleValue(record, "profit");
          revenue += getDoubleValue(record, "revenue");
        }
        
        report.setVolume(volume);
        report.setProfit(profit);
        report.setRevenue(revenue);
      }
      
      private double getDoubleValue(MapObject mapObject, String field) {
        String profitStr = mapObject.getString(field);
        if (!profitStr) {
            return 0.0
        }
        String cleaned = profitStr.replaceAll("[^0-9.-]", "")
        try {
            return Double.parseDouble(cleaned)
        } catch (NumberFormatException e) {
            println "Wrong number format: ${profitStr}"
            return 0.0
        }
    }
    };
    addRunnable(syncService);
  }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-06-19")

UpdateKeyAccountReport updateKeyAccountReport = new UpdateKeyAccountReport();
updateKeyAccountReport.run(reporter, scriptCtx);

return "DONE!!!"